/**
 * Job statistics interface for standardized logging
 */
export interface JobStats {
  jobType: string;
  processed: number;
  succeeded: number;
  failed: number;
  duration: number; // in milliseconds
  details?: Record<string, any>;
}

// Add a helper function to the logger with timestamps and emojis
export const logger = {
  info: (...args: any[]) =>
    console.log(`ℹ️ [INFO] [${new Date().toISOString()}]`, ...args),
  error: (...args: any[]) =>
    console.error(`❌ [ERROR] [${new Date().toISOString()}]`, ...args),
  warn: (...args: any[]) =>
    console.warn(`⚠️ [WARN] [${new Date().toISOString()}]`, ...args),
  debug: (...args: any[]) =>
    console.debug(`🔍 [DEBUG] [${new Date().toISOString()}]`, ...args), // enabled by default

  /**
   * Log job statistics in a standardized format for easier parsing
   * @param stats Job statistics object
   */
  jobStats: (stats: JobStats) => {
    const durationSeconds = (stats.duration / 1000).toFixed(2);
    const successRate =
      stats.processed > 0
        ? ((stats.succeeded / stats.processed) * 100).toFixed(1)
        : "0.0";

    const timestamp = new Date().toISOString();

    console.log(
      `📊 [JOB_STATS] [${timestamp}] ${stats.jobType} | ` +
        `PROCESSED:${stats.processed} | ` +
        `SUCCEEDED:${stats.succeeded} | ` +
        `FAILED:${stats.failed} | ` +
        `DURATION:${durationSeconds}s | ` +
        `SUCCESS_RATE:${successRate}%`
    );

    // Log additional details if provided
    if (stats.details && Object.keys(stats.details).length > 0) {
      console.log(
        `🔍 [JOB_DETAILS] [${timestamp}] ${stats.jobType} | ${JSON.stringify(stats.details)}`
      );
    }
  },
};

process.on("unhandledRejection", (reason) => {
  logger.error("Unhandled Promise Rejection:", reason);
});

process.on("uncaughtException", (error) => {
  logger.error("Uncaught Exception:", error);
});

// Add a global statistics tracker
export const globalJobStats = {
  dailyStats: {
    totalProcessed: 0,
    totalSucceeded: 0,
    totalFailed: 0,
    totalDuration: 0,
    jobTypes: new Map<
      string,
      { count: number; success: number; failure: number }
    >(),
    failureReasons: new Map<string, number>(),
    lastReset: new Date(),
  },

  // Reset stats (called at midnight or when explicitly needed)
  resetDailyStats() {
    this.dailyStats = {
      totalProcessed: 0,
      totalSucceeded: 0,
      totalFailed: 0,
      totalDuration: 0,
      jobTypes: new Map<
        string,
        { count: number; success: number; failure: number }
      >(),
      failureReasons: new Map<string, number>(),
      lastReset: new Date(),
    };
    logger.info(`📊 Daily job statistics reset at ${new Date().toISOString()}`);
  },

  // Add job result to statistics
  trackJob(
    jobType: string,
    success: boolean,
    duration: number,
    failureReason?: string
  ) {
    // Increment total counts
    this.dailyStats.totalProcessed++;
    if (success) {
      this.dailyStats.totalSucceeded++;
    } else {
      this.dailyStats.totalFailed++;
    }
    this.dailyStats.totalDuration += duration;

    // Track by job type
    if (!this.dailyStats.jobTypes.has(jobType)) {
      this.dailyStats.jobTypes.set(jobType, {
        count: 0,
        success: 0,
        failure: 0,
      });
    }
    const typeStats = this.dailyStats.jobTypes.get(jobType)!;
    typeStats.count++;
    if (success) {
      typeStats.success++;
    } else {
      typeStats.failure++;

      // Track failure reason if provided
      if (failureReason) {
        const currentCount =
          this.dailyStats.failureReasons.get(failureReason) ?? 0;
        this.dailyStats.failureReasons.set(failureReason, currentCount + 1);
      }
    }

    // Log the current stats for debugging
    logger.info(
      `📊 Current job stats: ${this.dailyStats.totalProcessed} processed, ${this.dailyStats.totalSucceeded} succeeded, ${this.dailyStats.totalFailed} failed`
    );
  },

  // Get formatted stats for email
  getFormattedStats() {
    return {
      totalProcessed: this.dailyStats.totalProcessed,
      totalSucceeded: this.dailyStats.totalSucceeded,
      totalFailed: this.dailyStats.totalFailed,
      totalDuration: this.dailyStats.totalDuration,
      jobTypes: Array.from(this.dailyStats.jobTypes.entries())
        .map(([type, data]) => ({
          type,
          count: data.count,
          success: data.success,
          failure: data.failure,
        }))
        .sort((a, b) => b.count - a.count),
      failureReasons: Array.from(this.dailyStats.failureReasons.entries())
        .map(([reason, count]) => ({
          reason,
          count,
        }))
        .sort((a, b) => b.count - a.count),
    };
  },
};
