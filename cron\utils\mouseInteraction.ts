// cron/utils/mouseInteraction.ts
import { Page } from "playwright";
import { logger } from "./logger";
import { humanDelay } from "./humanBehavior";

// Add type declarations for window properties
declare global {
  interface Window {
    mouseX: number;
    mouseY: number;
  }
}

/**
 * Generate a random point within the viewport
 */
function getRandomPoint(viewportWidth: number, viewportHeight: number) {
  return {
    x: Math.floor(Math.random() * viewportWidth),
    y: Math.floor(Math.random() * viewportHeight),
  };
}

/**
 * Generate a smooth path between two points using Bezier curves
 * This creates a more natural mouse movement path
 */
function generateSmoothPath(
  startX: number,
  startY: number,
  endX: number,
  endY: number,
  steps: number = 20
): Array<{ x: number; y: number }> {
  // Create control points for the Bezier curve
  // These points determine the curvature of the path
  const cp1x = startX + (Math.random() * 0.5 + 0.25) * (endX - startX);
  const cp1y = startY + (Math.random() * 0.8 - 0.4) * (endY - startY);
  const cp2x = endX - (Math.random() * 0.5 + 0.25) * (endX - startX);
  const cp2y = endY + (Math.random() * 0.8 - 0.4) * (endY - startY);

  const path = [];

  // Calculate points along the Bezier curve
  for (let i = 0; i <= steps; i++) {
    const t = i / steps;

    // Cubic Bezier formula
    const x =
      Math.pow(1 - t, 3) * startX +
      3 * Math.pow(1 - t, 2) * t * cp1x +
      3 * (1 - t) * Math.pow(t, 2) * cp2x +
      Math.pow(t, 3) * endX;

    const y =
      Math.pow(1 - t, 3) * startY +
      3 * Math.pow(1 - t, 2) * t * cp1y +
      3 * (1 - t) * Math.pow(t, 2) * cp2y +
      Math.pow(t, 3) * endY;

    path.push({ x: Math.round(x), y: Math.round(y) });
  }

  return path;
}

/**
 * Add subtle jitter to mouse movements to make them more human-like
 */
function addJitter(
  path: Array<{ x: number; y: number }>,
  jitterAmount: number = 2
): Array<{ x: number; y: number }> {
  return path.map((point) => ({
    x: point.x + (Math.random() * 2 - 1) * jitterAmount,
    y: point.y + (Math.random() * 2 - 1) * jitterAmount,
  }));
}

/**
 * Simulate human-like mouse movement between two points
 */
async function moveMouseSmoothly(
  page: Page,
  startX: number,
  startY: number,
  endX: number,
  endY: number,
  duration: number = 500
): Promise<void> {
  // Generate a smooth path with some jitter
  const steps = Math.floor(duration / 20); // One point every ~20ms
  const smoothPath = generateSmoothPath(startX, startY, endX, endY, steps);
  const path = addJitter(smoothPath);

  // Calculate delay between points to match the desired duration
  const pointDelay = duration / path.length;

  // Move the mouse along the path
  for (const point of path) {
    await page.mouse.move(point.x, point.y);
    await new Promise((resolve) => setTimeout(resolve, pointDelay));
  }
}

/**
 * Perform random mouse movements on the page to appear human-like
 */
export async function performRandomMouseMovements(
  page: Page,
  count: number = 3
): Promise<void> {
  try {
    logger.info(`🖱️ Performing ${count} random mouse movements...`);

    // Get viewport dimensions
    let viewportSize = page.viewportSize();
    if (!viewportSize) {
      logger.warn("⚠️ Could not get viewport size, using default values");
      viewportSize = { width: 1280, height: 720 };
    }

    // Current mouse position (start at a random point)
    let currentPosition = getRandomPoint(
      viewportSize.width,
      viewportSize.height
    );

    // Move mouse to initial position
    await page.mouse.move(currentPosition.x, currentPosition.y);

    // Perform random movements
    for (let i = 0; i < count; i++) {
      // Generate a new random point
      const nextPosition = getRandomPoint(
        viewportSize.width,
        viewportSize.height
      );

      // Calculate a random duration (300-800ms)
      const duration = 300 + Math.floor(Math.random() * 500);

      // Move the mouse smoothly to the next position
      await moveMouseSmoothly(
        page,
        currentPosition.x,
        currentPosition.y,
        nextPosition.x,
        nextPosition.y,
        duration
      );

      // Update current position
      currentPosition = nextPosition;

      // Add a small delay between movements
      await humanDelay("click");
    }

    logger.info("✅ Random mouse movements completed");
  } catch (error) {
    logger.error(`❌ Error performing random mouse movements: ${error}`);
  }
}

/**
 * Hover over random elements on the page
 */
export async function hoverRandomElements(
  page: Page,
  count: number = 2
): Promise<void> {
  try {
    logger.info(`🖱️ Hovering over ${count} random elements...`);

    // Find all clickable elements
    const elements = await page.$$(
      'a, button, input, select, [role="button"], [tabindex="0"]'
    );

    if (elements.length === 0) {
      logger.info("No interactive elements found on page");
      return;
    }

    // Shuffle the elements array
    const shuffled = [...elements].sort(() => Math.random() - 0.5);

    // Hover over random elements
    for (let i = 0; i < Math.min(count, shuffled.length); i++) {
      const element = shuffled[i];

      // Check if element is visible
      const isVisible = await element.isVisible().catch(() => false);

      if (isVisible) {
        // Get element position
        const boundingBox = await element.boundingBox();

        if (boundingBox) {
          // Get current mouse position
          const mousePosition = await page.evaluate(() => {
            return { x: window.mouseX || 0, y: window.mouseY || 0 };
          });

          // Calculate target position (center of element)
          const targetX = boundingBox.x + boundingBox.width / 2;
          const targetY = boundingBox.y + boundingBox.height / 2;

          // Move mouse smoothly to the element
          await moveMouseSmoothly(
            page,
            mousePosition.x,
            mousePosition.y,
            targetX,
            targetY,
            300 + Math.floor(Math.random() * 500)
          );

          // Hover over the element
          await element.hover({ force: true }).catch(() => {});

          // Wait a bit while hovering
          await humanDelay("click");
        }
      }
    }

    logger.info("✅ Random element hovering completed");
  } catch (error) {
    logger.error(`❌ Error hovering over random elements: ${error}`);
  }
}

/**
 * Perform random scrolls on the page
 */
export async function performRandomScrolls(
  page: Page,
  count: number = 2
): Promise<void> {
  try {
    logger.info(`📜 Performing ${count} random scrolls...`);

    for (let i = 0; i < count; i++) {
      // Generate random scroll amount (100-500px)
      const scrollAmount = 100 + Math.floor(Math.random() * 400);

      // Decide scroll direction (70% down, 30% up)
      const direction = Math.random() < 0.7 ? 1 : -1;
      const finalAmount = scrollAmount * direction;

      // Use a simpler scroll approach to avoid the __name error
      // Scroll in smaller increments to simulate smooth scrolling
      const steps = 10;
      const stepSize = finalAmount / steps;
      const stepDelay = 50; // ms between steps

      for (let step = 0; step < steps; step++) {
        await page.evaluate((stepSize) => {
          window.scrollBy(0, stepSize);
        }, stepSize);

        // Small delay between scroll steps
        await new Promise((resolve) => setTimeout(resolve, stepDelay));
      }

      // Wait a bit between scrolls
      await humanDelay("scroll");
    }

    logger.info("✅ Random scrolls completed");
  } catch (error) {
    logger.error(`❌ Error performing random scrolls: ${error}`);
  }
}

/**
 * Simulate user interaction before making network requests
 * This makes the browser appear more human-like
 */
export async function simulateHumanInteraction(page: Page): Promise<void> {
  try {
    logger.info("👤 Simulating human interaction before network requests...");

    // Perform random mouse movements
    await performRandomMouseMovements(page, 3);

    // Hover over random elements
    await hoverRandomElements(page, 2);

    // Perform random scrolls
    await performRandomScrolls(page, 2);

    // Add a small delay before proceeding
    await humanDelay();

    logger.info("✅ Human interaction simulation completed");
  } catch (error) {
    logger.error(`❌ Error simulating human interaction: ${error}`);
  }
}
