// cron/utils/playwrightStealth.ts
import { chromium, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Page } from "playwright-extra";
import StealthPlugin from "puppeteer-extra-plugin-stealth";
import FingerprintJS from "@fingerprintjs/fingerprintjs";
import { logger } from "./logger";
import { applyAdvancedFingerprinting } from "./advancedFingerprinting";

// Add the stealth plugin to playwright
chromium.use(StealthPlugin());

/**
 * Launch a stealth browser that's harder to detect as automated
 */
export async function launchStealthBrowser(
  headless: boolean = false,
  slowMo: number = 50,
  proxyServer?: string
): Promise<Browser> {
  try {
    // Force headless mode in production or if SCRAPER_HEADLESS is set to true
    const forceHeadless =
      process.env.SCRAPER_HEADLESS === "true" ||
      process.env.NODE_ENV === "production";
    const finalHeadless = forceHeadless ? true : headless;
    const finalSlowMo = finalHeadless ? Math.min(slowMo, 30) : slowMo; // Reduce slowMo in headless mode

    logger.info(
      `🥷 Launching stealth browser with anti-detection features (headless: ${finalHeadless})...`
    );

    // Launch with minimal arguments to appear as a real user
    // In production, use the bundled Chromium instead of trying to use system Chrome
    const browser = await chromium.launch({
      headless: finalHeadless,
      slowMo: finalSlowMo,
      // Only use 'chrome' channel in development, not in production
      channel: process.env.NODE_ENV === "production" ? undefined : "chrome",
      proxy: proxyServer ? { server: proxyServer } : undefined,
      args: [
        // These are the only arguments a typical Chrome user would have
        "--disable-blink-features=AutomationControlled",
        "--no-sandbox",
      ],
    });

    logger.info("✅ Stealth browser launched successfully");
    return browser;
  } catch (error) {
    logger.error(`❌ Failed to launch stealth browser: ${error}`);
    throw error;
  }
}

/**
 * Create a new stealth browser context with advanced fingerprinting
 */
export async function createStealthContext(
  browser: Browser,
  options: {
    viewport?: { width: number; height: number };
    userAgent?: string;
    locale?: string;
    timezoneId?: string;
    deviceScaleFactor?: number;
    httpCredentials?: { username: string; password: string };
  } = {}
): Promise<BrowserContext> {
  try {
    logger.info("🥷 Creating stealth browser context...");

    // Create a new context with the provided options
    const context = await browser.newContext({
      viewport: options.viewport,
      // Use a completely standard user agent that matches real Chrome users
      userAgent:
        options.userAgent ||
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      locale: options.locale || "en-US",
      timezoneId: options.timezoneId || "America/New_York",
      deviceScaleFactor: options.deviceScaleFactor || 1,
      httpCredentials: options.httpCredentials,
      // Additional options to make the browser harder to detect
      javaScriptEnabled: true,
      // Reduce the number of non-standard options
      ignoreHTTPSErrors: true,
      permissions: ["geolocation"],
      colorScheme: "light",
      isMobile: false,
      hasTouch: false,
    });

    // Apply additional stealth features at the context level
    await context.addInitScript(() => {
      // Override the navigator properties
      const overrideNavigator = () => {
        // Make navigator.webdriver not detectable
        Object.defineProperty(navigator, "webdriver", {
          get: () => false,
          configurable: true,
        });

        // Make plugins appear more natural
        Object.defineProperty(navigator, "plugins", {
          get: () => {
            const plugins = [
              {
                name: "Chrome PDF Plugin",
                filename: "internal-pdf-viewer",
                description: "Portable Document Format",
              },
              {
                name: "Chrome PDF Viewer",
                filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                description: "Portable Document Format",
              },
              {
                name: "Native Client",
                filename: "internal-nacl-plugin",
                description: "Native Client Executable",
              },
            ];

            // Create a simple plugins array without using deprecated PluginArray
            const pluginArray: any = {
              length: plugins.length,
              // Add required methods
              item: (index: number) => pluginArray[index] || null,
              namedItem: (name: string) => pluginArray[name] || null,
              refresh: () => {},
            };

            // Add each plugin to the array
            plugins.forEach((plugin, i) => {
              // Create a simple plugin object without using deprecated Plugin
              const pluginObj: any = {
                name: plugin.name,
                filename: plugin.filename,
                description: plugin.description,
                length: 1,
                // Add required methods
                item: (index: number) => (index === 0 ? pluginObj : null),
                namedItem: (name: string) =>
                  name === plugin.name ? pluginObj : null,
              };

              // Add the plugin to the array
              pluginArray[i] = pluginObj;
              pluginArray[plugin.name] = pluginObj;
            });

            return pluginArray;
          },
          configurable: true,
        });

        // Make languages appear more natural
        Object.defineProperty(navigator, "languages", {
          get: () => ["en-US", "en"],
          configurable: true,
        });
      };

      // Override the permissions API
      const overridePermissions = () => {
        if (navigator.permissions) {
          const originalQuery = navigator.permissions.query;
          navigator.permissions.query = function (
            parameters: any
          ): Promise<any> {
            if (parameters.name === "notifications") {
              return Promise.resolve({
                state: "granted",
                onchange: null,
              } as any);
            }
            return originalQuery.call(this, parameters);
          };
        }
      };

      // Override WebGL to prevent fingerprinting
      const overrideWebGL = () => {
        const getParameterProxyHandler = {
          apply: function (target: any, ctx: any, args: any[]) {
            const param = args[0];

            // UNMASKED_VENDOR_WEBGL and UNMASKED_RENDERER_WEBGL are commonly used for fingerprinting
            if (param === 37445) {
              // UNMASKED_VENDOR_WEBGL
              return "Google Inc. (Intel)";
            }
            if (param === 37446) {
              // UNMASKED_RENDERER_WEBGL
              return "ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)";
            }

            return Reflect.apply(target, ctx, args);
          },
        };

        // Apply the proxy to WebGL getParameter
        if (window.WebGLRenderingContext) {
          const prototype = WebGLRenderingContext.prototype;
          const getParameter = prototype.getParameter;
          prototype.getParameter = new Proxy(
            getParameter,
            getParameterProxyHandler
          );
        }
      };

      // Override canvas fingerprinting
      const overrideCanvas = () => {
        // Add some noise to canvas fingerprinting
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        const originalGetImageData =
          CanvasRenderingContext2D.prototype.getImageData;

        // Add subtle noise to canvas data
        HTMLCanvasElement.prototype.toDataURL = function (_type: string) {
          // Only add noise if it's likely being used for fingerprinting
          if (this.width === 16 && this.height === 16) {
            const context = this.getContext("2d");
            if (context) {
              // Add subtle noise that won't be visually noticeable
              const imageData = context.getImageData(
                0,
                0,
                this.width,
                this.height
              );
              const data = imageData.data;

              for (let i = 0; i < data.length; i += 4) {
                // Only modify alpha channel slightly
                data[i + 3] = data[i + 3] > 0 ? data[i + 3] - 1 : data[i + 3];
              }

              context.putImageData(imageData, 0, 0);
            }
          }

          return originalToDataURL.apply(this, arguments as any);
        };

        // Add subtle noise to getImageData
        CanvasRenderingContext2D.prototype.getImageData = function () {
          const imageData = originalGetImageData.apply(this, arguments as any);

          // Only add noise if it's likely being used for fingerprinting
          if (arguments[2] === 16 && arguments[3] === 16) {
            const data = imageData.data;

            for (let i = 0; i < data.length; i += 4) {
              // Only modify alpha channel slightly
              data[i + 3] = data[i + 3] > 0 ? data[i + 3] - 1 : data[i + 3];
            }
          }

          return imageData;
        };
      };

      // Override audio fingerprinting
      const overrideAudio = () => {
        if (window.AudioContext || (window as any).webkitAudioContext) {
          const AudioContext =
            window.AudioContext || (window as any).webkitAudioContext;
          const originalGetChannelData = AudioBuffer.prototype.getChannelData;
          const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
          const originalGetFloatFrequencyData =
            AnalyserNode.prototype.getFloatFrequencyData;

          // Add subtle noise to audio data
          AudioBuffer.prototype.getChannelData = function (channel) {
            const data = originalGetChannelData.call(this, channel);

            // Only add noise if it's a short buffer (likely used for fingerprinting)
            if (this.length < 1000) {
              const noise = 0.0001; // Very subtle noise

              // Create a new Float32Array and add noise
              const noiseData = new Float32Array(data.length);
              for (let i = 0; i < data.length; i++) {
                noiseData[i] = data[i] + (Math.random() * 2 - 1) * noise;
              }

              return noiseData;
            }

            return data;
          };

          // Override the analyser to add noise
          AudioContext.prototype.createAnalyser = function () {
            const analyser = originalCreateAnalyser.call(this);

            // Override getFloatFrequencyData to add noise
            analyser.getFloatFrequencyData = function (array) {
              originalGetFloatFrequencyData.call(this, array);

              // Add subtle noise
              for (let i = 0; i < array.length; i++) {
                array[i] += Math.random() * 0.1 - 0.05;
              }

              return array;
            };

            return analyser;
          };
        }
      };

      // Apply all the overrides
      overrideNavigator();
      overridePermissions();
      overrideWebGL();
      overrideCanvas();
      overrideAudio();

      // Hide automation flags
      delete (window as any).cdc_adoQpoasnfa76pfcZLmcfl_Array;
      delete (window as any).cdc_adoQpoasnfa76pfcZLmcfl_Promise;
      delete (window as any).cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
    });

    logger.info("✅ Stealth browser context created successfully");
    return context;
  } catch (error) {
    logger.error(`❌ Failed to create stealth browser context: ${error}`);
    throw error;
  }
}

/**
 * Apply all stealth and fingerprinting techniques to a page
 */
export async function applyStealthFingerprinting(
  context: BrowserContext,
  page: Page,
  options: {
    viewport?: { width: number; height: number };
    userAgent?: string;
  } = {}
): Promise<void> {
  try {
    logger.info("🥷 Applying stealth fingerprinting to page...");

    // Apply our advanced fingerprinting
    await applyAdvancedFingerprinting(context, page, options);

    // Additional page-level stealth techniques
    await page.addInitScript(() => {
      // Hide that we're using automation
      Object.defineProperty(navigator, "webdriver", { get: () => false });

      // Initialize FingerprintJS for consistent fingerprinting
      const loadFingerprintJS = async () => {
        try {
          // Load FingerprintJS
          const fpPromise = FingerprintJS.load();
          const fp = await fpPromise;
          const result = await fp.get();

          // Store the visitor ID in localStorage for consistent identification
          window.localStorage.setItem("fpjs_visitor_id", result.visitorId);
          console.log("FingerprintJS visitor ID:", result.visitorId);

          // Return the components for more detailed fingerprinting
          return result.components;
        } catch (error) {
          console.error("Error loading FingerprintJS:", error);
          return null;
        }
      };

      // Execute FingerprintJS loading
      loadFingerprintJS();

      // Fake user interaction
      const originalFunction = HTMLElement.prototype.appendChild;
      HTMLElement.prototype.appendChild = function <T extends Node>(
        this: HTMLElement,
        node: T
      ): T {
        const element = originalFunction.apply(this, [node]) as T;
        // Simulate user interaction for specific elements
        if (
          element instanceof HTMLElement &&
          (element.tagName === "IFRAME" ||
            element.tagName === "IMG" ||
            element.tagName === "SCRIPT")
        ) {
          setTimeout(
            () => {
              element.dispatchEvent(new Event("mouseover", { bubbles: true }));
              element.dispatchEvent(new Event("mousedown", { bubbles: true }));
              element.dispatchEvent(new Event("mouseup", { bubbles: true }));
              element.dispatchEvent(new Event("auxclick", { bubbles: true }));
            },
            Math.floor(Math.random() * 500) + 100
          );
        }
        return element;
      };
    });

    logger.info("✅ Stealth fingerprinting applied successfully");
  } catch (error) {
    logger.error(`❌ Failed to apply stealth fingerprinting: ${error}`);
  }
}
