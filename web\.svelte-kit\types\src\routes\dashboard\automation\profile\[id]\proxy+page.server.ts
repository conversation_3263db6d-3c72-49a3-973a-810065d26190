// @ts-nocheck
import { redirect } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { getUserFromToken } from '$lib/server/auth.js';
import type { PageServerLoad } from '../../../$types.js';

// Using the shared Prisma client from $lib/server/prisma

export const load = async ({ params, cookies, locals }: Parameters<PageServerLoad>[0]) => {
  const user = getUserFromToken(cookies);
  const { id } = params;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  locals.user = user;

  // Get the profile with associated data
  const profile = await prisma.profile.findUnique({
    where: { id },
    include: {
      data: true,
      documents: {
        where: {
          type: 'resume',
        },
      },
    },
  });

  if (!profile || (profile.userId !== user.id && !profile.teamId)) {
    throw redirect(302, '/dashboard/automation');
  }

  // Get automation runs for this profile
  const automationRuns = []; // Empty for now until we have the database models

  // Get all resumes for the user
  const documents = await prisma.document.findMany({
    where: {
      OR: [
        { userId: user.id },
        {
          profile: {
            team: {
              members: {
                some: { userId: user.id },
              },
            },
          },
        },
      ],
      type: 'resume',
    },
    select: {
      id: true,
      label: true,
      fileName: true,
      createdAt: true,
    },
  });

  // Format resumes for the dropdown
  const resumes = documents.map((doc) => ({
    id: doc.id,
    label: doc.label || doc.fileName || `Resume (${new Date(doc.createdAt).toLocaleDateString()})`,
  }));

  return {
    user,
    profile,
    automationRuns,
    resumes,
  };
};
