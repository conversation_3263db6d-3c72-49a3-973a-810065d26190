// @ts-nocheck
import { redirect, fail } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import type { PageServerLoad, Actions } from './$types.js';

// Define the schema with Zod for validation
const accountSchema = z.object({
  // Personal Information
  name: z.string().min(1, 'Name is required').max(100, 'Name is too long'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional(),
  bio: z.string().max(500, 'Bio should be less than 500 characters').optional(),
  profilePicture: z.string().optional().nullable(),

  // Regional Settings
  language: z.string().default('en'),
  timezone: z.string().default('UTC'),
  dateFormat: z.enum(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']).default('MM/DD/YYYY'),
  timeFormat: z.enum(['12h', '24h']).default('12h'),

  // Accessibility
  theme: z.enum(['light', 'dark', 'system']).default('system'),
  highContrast: z.boolean().default(false),
  reducedMotion: z.boolean().default(false),
  largeText: z.boolean().default(false),
  screenReader: z.boolean().default(false),
  // UI Preferences (part of accessibility)
  viewMode: z.enum(['list', 'grid', 'compact']).optional(),

  // Privacy Settings
  profileVisibility: z.enum(['public', 'private', 'connections']).default('public'),
  activityVisibility: z.enum(['public', 'private', 'connections']).default('public'),
  allowDataCollection: z.boolean().default(true),
  allowThirdPartySharing: z.boolean().default(false),

  // Cookie Preferences
  cookiePreferences: z
    .object({
      functional: z.boolean().default(true),
      analytics: z.boolean().default(true),
      advertising: z.boolean().default(false),
    })
    .optional(),
});

export const load = async ({ locals }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  // Get user data with complete details
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
  });

  if (!userData) {
    throw redirect(302, '/auth/sign-in');
  }

  // Extract account preferences from user data
  const preferences = (userData.preferences as any) || {};
  const accountPrefs = preferences.account || {};

  const userPreferences = {
    // Personal Information
    name: userData.name || '',
    email: userData.email,
    phone: accountPrefs.phone,
    bio: accountPrefs.bio,
    profilePicture: userData.image,

    // Regional Settings
    language: accountPrefs.language,
    timezone: accountPrefs.timezone,
    dateFormat: accountPrefs.dateFormat,
    timeFormat: accountPrefs.timeFormat,

    // Accessibility
    theme: accountPrefs.accessibility?.theme,
    highContrast: accountPrefs.accessibility?.highContrast,
    reducedMotion: accountPrefs.accessibility?.reducedMotion,
    largeText: accountPrefs.accessibility?.largeText,
    screenReader: accountPrefs.accessibility?.screenReader,
    // UI Preferences
    viewMode: accountPrefs.accessibility?.viewMode,

    // Privacy Settings
    profileVisibility: accountPrefs.privacy?.profileVisibility,
    activityVisibility: accountPrefs.privacy?.activityVisibility,
    allowDataCollection: accountPrefs.privacy?.allowDataCollection,
    allowThirdPartySharing: accountPrefs.privacy?.allowThirdPartySharing,

    // Cookie Preferences
    cookiePreferences: accountPrefs.cookiePreferences,
  };

  // Create the form with initial values
  const form = await superValidate(userPreferences, zod(accountSchema));

  return {
    user: userData,
    form,
  };
};

export const actions = {
  default: async ({ request, locals }: import('./$types').RequestEvent) => {
    // Check if user is authenticated
    if (!locals.user) {
      throw redirect(302, '/auth/sign-in');
    }

    // Get user data
    const userData = await prisma.user.findUnique({
      where: { id: locals.user.id },
    });

    if (!userData) {
      throw redirect(302, '/auth/sign-in');
    }

    // Validate form data
    const form = await superValidate(request, zod(accountSchema));

    if (!form.valid) {
      return fail(400, { form });
    }

    try {
      // Get existing preferences or create an empty object
      const preferences = (userData.preferences as any) || {};

      // Update account preferences
      const updatedPreferences = {
        ...preferences,
        account: {
          ...(preferences.account || {}),
          phone: form.data.phone,
          bio: form.data.bio,
          language: form.data.language,
          timezone: form.data.timezone,
          dateFormat: form.data.dateFormat,
          timeFormat: form.data.timeFormat,
          accessibility: {
            theme: form.data.theme,
            highContrast: form.data.highContrast,
            reducedMotion: form.data.reducedMotion,
            largeText: form.data.largeText,
            screenReader: form.data.screenReader,
            // UI Preferences
            viewMode: form.data.viewMode,
          },
          privacy: {
            profileVisibility: form.data.profileVisibility,
            activityVisibility: form.data.activityVisibility,
            allowDataCollection: form.data.allowDataCollection,
            allowThirdPartySharing: form.data.allowThirdPartySharing,
          },
          cookiePreferences: form.data.cookiePreferences || {
            functional: true,
            analytics: true,
            advertising: false,
          },
        },
      };

      // Update the user's name and preferences in the database
      await prisma.user.update({
        where: { id: userData.id },
        data: {
          name: form.data.name,
          image: form.data.profilePicture,
          preferences: updatedPreferences,
        },
      });

      // Return the updated user data to force a refresh of the user data in the layout
      return {
        form,
        success: true,
        user: {
          ...userData,
          name: form.data.name,
          image: form.data.profilePicture,
          preferences: updatedPreferences,
        },
      };
    } catch (error) {
      console.error('Error updating account settings:', error);
      return fail(500, { form, error: 'Failed to update account settings' });
    }
  },
};
;null as any as Actions;