import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function addReferralFields() {
  try {
    console.log('🔄 Adding referral fields to database...');

    // Add referral fields to User table one by one
    try {
      await prisma.$executeRaw`ALTER TABLE "web"."User" ADD COLUMN "referralCode" TEXT;`;
    } catch (e) {
      console.log('referralCode column already exists');
    }

    try {
      await prisma.$executeRaw`ALTER TABLE "web"."User" ADD COLUMN "referredById" TEXT;`;
    } catch (e) {
      console.log('referredById column already exists');
    }

    try {
      await prisma.$executeRaw`ALTER TABLE "web"."User" ADD COLUMN "referralCount" INTEGER DEFAULT 0;`;
    } catch (e) {
      console.log('referralCount column already exists');
    }

    try {
      await prisma.$executeRaw`ALTER TABLE "web"."User" ADD COLUMN "referralRewards" JSONB DEFAULT '{}'::jsonb;`;
    } catch (e) {
      console.log('referralRewards column already exists');
    }

    // Add unique constraint on referralCode
    try {
      await prisma.$executeRaw`ALTER TABLE "web"."User" ADD CONSTRAINT "User_referralCode_key" UNIQUE ("referralCode");`;
    } catch (e) {
      console.log('referralCode unique constraint already exists');
    }

    // Create Referral table
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "web"."Referral" (
        "id" TEXT NOT NULL,
        "referrerId" TEXT NOT NULL,
        "referredId" TEXT NOT NULL,
        "referralCode" TEXT NOT NULL,
        "status" TEXT NOT NULL DEFAULT 'pending',
        "rewardType" TEXT,
        "rewardAmount" DOUBLE PRECISION,
        "rewardGiven" BOOLEAN NOT NULL DEFAULT false,
        "metadata" JSONB DEFAULT '{}'::jsonb,
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "completedAt" TIMESTAMP(3),

        CONSTRAINT "Referral_pkey" PRIMARY KEY ("id")
      );
    `;

    // Add unique constraint and indexes
    try {
      await prisma.$executeRaw`ALTER TABLE "web"."Referral" ADD CONSTRAINT "Referral_referrerId_referredId_key" UNIQUE ("referrerId", "referredId");`;
    } catch (e) {
      console.log('Referral unique constraint already exists');
    }

    try {
      await prisma.$executeRaw`CREATE INDEX "Referral_referralCode_idx" ON "web"."Referral"("referralCode");`;
    } catch (e) {
      console.log('Referral referralCode index already exists');
    }

    try {
      await prisma.$executeRaw`CREATE INDEX "Referral_status_idx" ON "web"."Referral"("status");`;
    } catch (e) {
      console.log('Referral status index already exists');
    }

    // Add foreign key constraints
    try {
      await prisma.$executeRaw`ALTER TABLE "web"."Referral" ADD CONSTRAINT "Referral_referrerId_fkey" FOREIGN KEY ("referrerId") REFERENCES "web"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;`;
    } catch (e) {
      console.log('Referral referrerId foreign key already exists');
    }

    try {
      await prisma.$executeRaw`ALTER TABLE "web"."Referral" ADD CONSTRAINT "Referral_referredId_fkey" FOREIGN KEY ("referredId") REFERENCES "web"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;`;
    } catch (e) {
      console.log('Referral referredId foreign key already exists');
    }

    try {
      await prisma.$executeRaw`ALTER TABLE "web"."User" ADD CONSTRAINT "User_referredById_fkey" FOREIGN KEY ("referredById") REFERENCES "web"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;`;
    } catch (e) {
      console.log('User referredById foreign key already exists');
    }

    console.log('✅ Referral fields added successfully!');
    console.log('📊 Database schema updated with:');
    console.log('   - User.referralCode (unique)');
    console.log('   - User.referredById (optional)');
    console.log('   - User.referralCount (default 0)');
    console.log('   - User.referralRewards (JSON)');
    console.log('   - Referral table with relationships');
  } catch (error) {
    console.error('❌ Error adding referral fields:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
addReferralFields()
  .then(() => {
    console.log('🎉 Referral system database setup complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Failed to setup referral system:', error);
    process.exit(1);
  });
