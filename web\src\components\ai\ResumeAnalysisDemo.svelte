<script lang="ts">
  import { But<PERSON> } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card';
  import * as Progress from '$lib/components/ui/progress';
  import { Badge } from '$lib/components/ui/badge';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { Textarea } from '$lib/components/ui/textarea';
  import * as Accordion from '$lib/components/ui/accordion';
  import {
    FileText,
    AlertTriangle,
    CheckCircle,
    RefreshCw,
    Upload,
    Sparkles,
    FileUp
  } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';
  import type { ATSAnalysisResult } from '$lib/services/ai-service';

  // State variables
  let isLoading = false;
  let error: string | null = null;
  let analysis: ATSAnalysisResult | null = null;
  let resumeText = '';
  let jobDescription = '';
  let uploadedFile: File | null = null;

  // Format score as a percentage
  function formatScore(score: number): number {
    return Math.round(score);
  }

  // Get color based on score
  function getScoreColor(score: number): string {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-amber-600';
    return 'text-red-600';
  }

  // Get label based on score
  function getScoreLabel(score: number): string {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Poor';
  }

  // Handle file upload
  async function handleFileUpload(event: Event) {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;
    
    uploadedFile = input.files[0];
    
    // Read file content
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        resumeText = e.target.result as string;
      }
    };
    reader.readAsText(uploadedFile);
  }

  // Analyze resume
  async function analyzeResume() {
    if (!resumeText) {
      toast.error('Please enter or upload resume text');
      return;
    }

    isLoading = true;
    error = null;

    try {
      const response = await fetch('/api/ai/ats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resumeText,
          jobDescription: jobDescription || undefined,
        }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }

      const data = await response.json();
      analysis = data.analysis;
      toast.success('Resume analysis completed');
    } catch (err) {
      error = err instanceof Error ? err.message : 'An error occurred';
      toast.error('Failed to analyze resume');
    } finally {
      isLoading = false;
    }
  }
</script>

<Card.Root class="w-full">
  <Card.Header>
    <Card.Title class="flex items-center">
      <FileText class="mr-2 h-5 w-5" />
      Resume Analysis Demo
    </Card.Title>
    <Card.Description>
      Test the complete flow from UI to Worker to AI Service
    </Card.Description>
  </Card.Header>

  <Card.Content>
    <div class="space-y-4">
      <!-- Resume Text Input -->
      <div>
        <Label for="resume-text">Resume Text</Label>
        <div class="flex items-center gap-2 mb-2">
          <Input 
            type="file" 
            id="resume-file" 
            accept=".txt,.pdf,.doc,.docx" 
            on:change={handleFileUpload} 
          />
          <Button variant="outline" class="flex items-center gap-2">
            <FileUp class="h-4 w-4" />
            Upload
          </Button>
        </div>
        <Textarea 
          id="resume-text" 
          bind:value={resumeText} 
          placeholder="Paste your resume text here or upload a file"
          rows={8}
        />
      </div>

      <!-- Job Description Input -->
      <div>
        <Label for="job-description">Job Description (Optional)</Label>
        <Textarea 
          id="job-description" 
          bind:value={jobDescription} 
          placeholder="Paste the job description here for more targeted analysis"
          rows={4}
        />
      </div>

      <!-- Analyze Button -->
      <Button 
        on:click={analyzeResume} 
        disabled={isLoading || !resumeText}
        class="w-full"
      >
        {#if isLoading}
          <RefreshCw class="mr-2 h-4 w-4 animate-spin" />
          Analyzing...
        {:else}
          <Sparkles class="mr-2 h-4 w-4" />
          Analyze Resume
        {/if}
      </Button>

      <!-- Analysis Results -->
      {#if isLoading}
        <div class="flex items-center justify-center py-6">
          <div class="h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
          <span class="ml-2">Analyzing resume...</span>
        </div>
      {:else if error}
        <div class="rounded-md bg-red-50 p-4 text-sm text-red-500">
          <p>Error: {error}</p>
          <Button
            variant="outline"
            class="mt-2"
            on:click={analyzeResume}>
            Try Again
          </Button>
        </div>
      {:else if analysis}
        <!-- Overall Score -->
        <div class="mb-6 rounded-lg bg-gray-50 p-4">
          <div class="mb-2 text-center">
            <h3 class="text-lg font-medium">Overall ATS Score</h3>
            <div class="mt-2 text-4xl font-bold {getScoreColor(analysis.overallScore)}">
              {formatScore(analysis.overallScore)}%
            </div>
            <div class="mt-1 text-sm text-gray-500">
              {getScoreLabel(analysis.overallScore)}
            </div>
          </div>
        </div>

        <!-- Score Breakdown -->
        <div class="mb-4">
          <h4 class="mb-2 font-medium">Score Breakdown</h4>
          <div class="space-y-3">
            <div>
              <div class="flex items-center justify-between text-sm">
                <span>Keywords</span>
                <span class="{getScoreColor(analysis.keywordScore)}">{formatScore(analysis.keywordScore)}%</span>
              </div>
              <Progress.Root value={analysis.keywordScore} class="h-2">
                <Progress.Indicator style={`transform: translateX(-${100 - analysis.keywordScore}%)`} />
              </Progress.Root>
            </div>
            <div>
              <div class="flex items-center justify-between text-sm">
                <span>Format</span>
                <span class="{getScoreColor(analysis.formatScore)}">{formatScore(analysis.formatScore)}%</span>
              </div>
              <Progress.Root value={analysis.formatScore} class="h-2">
                <Progress.Indicator style={`transform: translateX(-${100 - analysis.formatScore}%)`} />
              </Progress.Root>
            </div>
            <div>
              <div class="flex items-center justify-between text-sm">
                <span>Content</span>
                <span class="{getScoreColor(analysis.contentScore)}">{formatScore(analysis.contentScore)}%</span>
              </div>
              <Progress.Root value={analysis.contentScore} class="h-2">
                <Progress.Indicator style={`transform: translateX(-${100 - analysis.contentScore}%)`} />
              </Progress.Root>
            </div>
            <div>
              <div class="flex items-center justify-between text-sm">
                <span>Readability</span>
                <span class="{getScoreColor(analysis.readabilityScore)}">{formatScore(analysis.readabilityScore)}%</span>
              </div>
              <Progress.Root value={analysis.readabilityScore} class="h-2">
                <Progress.Indicator style={`transform: translateX(-${100 - analysis.readabilityScore}%)`} />
              </Progress.Root>
            </div>
          </div>
        </div>

        <!-- Detailed Analysis -->
        <Accordion.Root class="w-full">
          <Accordion.Item value="keywords">
            <Accordion.Trigger>Keywords</Accordion.Trigger>
            <Accordion.Content>
              <div class="space-y-2 p-2">
                <h5 class="font-medium">Matched Keywords</h5>
                <div class="flex flex-wrap gap-1">
                  {#each analysis.keywordMatches as keyword}
                    <Badge variant="outline" class="bg-green-50">{keyword}</Badge>
                  {/each}
                </div>
                
                <h5 class="font-medium mt-4">Missing Keywords</h5>
                <div class="flex flex-wrap gap-1">
                  {#each analysis.missingKeywords as keyword}
                    <Badge variant="outline" class="bg-red-50">{keyword}</Badge>
                  {/each}
                </div>
              </div>
            </Accordion.Content>
          </Accordion.Item>
          
          <Accordion.Item value="format">
            <Accordion.Trigger>Format Issues</Accordion.Trigger>
            <Accordion.Content>
              <ul class="list-disc pl-5 space-y-1">
                {#each analysis.formatIssues as issue}
                  <li>{issue}</li>
                {/each}
              </ul>
            </Accordion.Content>
          </Accordion.Item>
          
          <Accordion.Item value="content">
            <Accordion.Trigger>Content Suggestions</Accordion.Trigger>
            <Accordion.Content>
              <ul class="list-disc pl-5 space-y-1">
                {#each analysis.contentSuggestions as suggestion}
                  <li>{suggestion}</li>
                {/each}
              </ul>
            </Accordion.Content>
          </Accordion.Item>
          
          <Accordion.Item value="readability">
            <Accordion.Trigger>Readability Suggestions</Accordion.Trigger>
            <Accordion.Content>
              <ul class="list-disc pl-5 space-y-1">
                {#each analysis.readabilitySuggestions as suggestion}
                  <li>{suggestion}</li>
                {/each}
              </ul>
            </Accordion.Content>
          </Accordion.Item>
        </Accordion.Root>
      {/if}
    </div>
  </Card.Content>
</Card.Root>
