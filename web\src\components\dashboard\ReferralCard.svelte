<script lang="ts">
  import { onMount } from 'svelte';
  import { toast } from 'svelte-sonner';
  import { Card } from '$lib/components/ui/card/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { Share2, Users, Copy, ExternalLink } from 'lucide-svelte';
  import ReferralModal from '$components/modals/ReferralModal.svelte';

  let referralData: any = null;
  let loading = true;
  let showModal = false;
  let copying = false;

  // Load referral data
  const loadReferralData = async () => {
    try {
      const response = await fetch('/api/referrals');
      if (response.ok) {
        referralData = await response.json();
      } else {
        console.error('Failed to load referral data');
      }
    } catch (error) {
      console.error('Error loading referral data:', error);
    } finally {
      loading = false;
    }
  };

  // Copy referral link to clipboard
  const copyReferralLink = async () => {
    if (!referralData?.referralLink) return;
    
    copying = true;
    try {
      await navigator.clipboard.writeText(referralData.referralLink);
      toast.success('Referral link copied!');
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast.error('Failed to copy referral link');
    } finally {
      copying = false;
    }
  };

  onMount(() => {
    loadReferralData();
  });
</script>

<Card.Root class="relative overflow-hidden">
  <Card.Header class="pb-3">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <Share2 class="h-5 w-5 text-primary" />
        <Card.Title class="text-lg">Referral Program</Card.Title>
      </div>
      <Button
        variant="ghost"
        size="sm"
        on:click={() => (showModal = true)}
        class="h-8 w-8 p-0">
        <ExternalLink class="h-4 w-4" />
      </Button>
    </div>
    <Card.Description>
      Share Hirli with friends and earn rewards
    </Card.Description>
  </Card.Header>

  <Card.Content class="space-y-4">
    {#if loading}
      <div class="flex items-center justify-center py-4">
        <div class="text-sm text-muted-foreground">Loading...</div>
      </div>
    {:else if referralData}
      <!-- Quick Stats -->
      <div class="grid grid-cols-2 gap-4">
        <div class="text-center p-3 bg-muted/50 rounded-lg">
          <div class="text-xl font-bold">{referralData.referralCount || 0}</div>
          <div class="text-xs text-muted-foreground">Referrals</div>
        </div>
        <div class="text-center p-3 bg-muted/50 rounded-lg">
          <div class="text-lg font-bold font-mono">{referralData.referralCode}</div>
          <div class="text-xs text-muted-foreground">Your Code</div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          class="flex-1"
          on:click={copyReferralLink}
          disabled={copying}>
          <Copy class="mr-2 h-3 w-3" />
          Copy Link
        </Button>
        <Button
          variant="outline"
          size="sm"
          class="flex-1"
          on:click={() => (showModal = true)}>
          <Users class="mr-2 h-3 w-3" />
          View All
        </Button>
      </div>

      <!-- Recent Referrals Preview -->
      {#if referralData.referrals && referralData.referrals.length > 0}
        <div class="space-y-2">
          <div class="text-sm font-medium">Recent Referrals</div>
          {#each referralData.referrals.slice(0, 2) as referral}
            <div class="flex items-center justify-between text-sm">
              <span class="truncate">{referral.referred.name || referral.referred.email}</span>
              <Badge variant={referral.status === 'completed' ? 'default' : 'secondary'} class="text-xs">
                {referral.status}
              </Badge>
            </div>
          {/each}
          {#if referralData.referrals.length > 2}
            <div class="text-xs text-muted-foreground text-center">
              +{referralData.referrals.length - 2} more
            </div>
          {/if}
        </div>
      {:else}
        <div class="text-center py-4">
          <Users class="h-8 w-8 text-muted-foreground mx-auto mb-2" />
          <p class="text-sm text-muted-foreground">No referrals yet</p>
          <p class="text-xs text-muted-foreground">Start sharing to earn rewards!</p>
        </div>
      {/if}
    {:else}
      <div class="text-center py-4">
        <div class="text-sm text-muted-foreground">Failed to load referral data</div>
      </div>
    {/if}
  </Card.Content>

  <Card.Footer class="pt-3">
    <Button
      variant="default"
      size="sm"
      class="w-full"
      href="/dashboard/settings/referrals">
      Manage Referrals
    </Button>
  </Card.Footer>
</Card.Root>

<!-- Referral Modal -->
<ReferralModal
  bind:open={showModal}
  onClose={() => (showModal = false)} />
