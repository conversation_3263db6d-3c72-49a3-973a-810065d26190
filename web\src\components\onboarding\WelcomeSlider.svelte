<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import * as Carousel from '$lib/components/ui/carousel/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import * as Checkbox from '$lib/components/ui/checkbox/index.js';
  import {
    ChevronLeft,
    ChevronRight,
    Search,
    FileText,
    Briefcase,
    Target,
    CheckCircle,
    X,
    Rocket,
  } from 'lucide-svelte';
  import { onMount, onDestroy } from 'svelte';

  // Props
  let { open = false, onClose = () => {}, userData = null } = $props();

  // State
  let currentStep = $state(0);
  const totalSteps = 5;
  let api = $state<any>(null);
  let dontShowAgain = $state(false);
  let carouselInitialized = $state(false);
  let globalRunOnceGuard = $state(false);

  // Welcome steps
  const steps = [
    {
      title: 'Welcome to Hirl<PERSON>!',
      description:
        "We're excited to have you on board. Let's take a quick tour to help you get started.",
      icon: Rocket,
      color: 'text-blue-500',
    },
    {
      title: 'Find Your Perfect Job',
      description: 'Use our powerful search to find jobs that match your skills and experience.',
      icon: Search,
      color: 'text-purple-500',
    },
    {
      title: 'Upload Your Resume',
      description: 'Upload your resume to get personalized job matches and make applying easier.',
      icon: FileText,
      color: 'text-green-500',
    },
    {
      title: 'Track Your Applications',
      description: 'Keep track of all your job applications in one place.',
      icon: Briefcase,
      color: 'text-amber-500',
    },
    {
      title: 'Get Matched to Jobs',
      description: 'Our AI will match you with jobs that fit your skills and experience.',
      icon: Target,
      color: 'text-red-500',
    },
  ];

  // Functions
  function nextStep() {
    if (currentStep < totalSteps - 1) {
      currentStep++;
      if (api && carouselInitialized) {
        api.scrollTo(currentStep);
      }
    } else {
      completeOnboarding();
    }
  }

  function prevStep() {
    if (currentStep > 0) {
      currentStep--;
      if (api && carouselInitialized) {
        api.scrollTo(currentStep);
      }
    }
  }

  function completeOnboarding() {
    // Save to localStorage that the user has completed onboarding
    if (typeof localStorage !== 'undefined') {
      if (dontShowAgain) {
        localStorage.setItem('onboardingCompleted', 'true');
      }
      // Always mark the welcome toast as shown
      localStorage.setItem('welcomeToastShown', 'true');
    }
    onClose();
  }

  function skipOnboarding() {
    completeOnboarding();
  }

  // Handle carousel API
  function handleCarouselApi(a: any) {
    api = a;

    if (api) {
      carouselInitialized = true;

      // Set up event listener for slide changes
      api.on('select', () => {
        // Update currentStep based on the carousel's selected index
        currentStep = api.selectedScrollSnap();
      });

      // Select the first slide
      if (currentStep === 0) {
        api.scrollTo(0);
      }
    }
  }

  // Check if this is the first time the user is visiting
  onMount(() => {
    if (typeof localStorage !== 'undefined' && !globalRunOnceGuard) {
      globalRunOnceGuard = true;
      const completed = localStorage.getItem('onboardingCompleted');
      if (!completed && userData) {
        open = true;
      }
    }
  });

  // Clean up when component is destroyed
  onDestroy(() => {
    // Clean up any event listeners or references
    if (api) {
      api = null;
    }
    carouselInitialized = false;
  });

  // Watch for dialog open/close
  $effect(() => {
    if (!open) {
      // Reset state when dialog is closed
      currentStep = 0;
    }
  });
</script>

<Dialog.Root bind:open>
  <Dialog.Portal>
    <Dialog.Overlay class="bg-background/80 backdrop-blur-sm" />
    <Dialog.Content class="gap-0 p-0 sm:w-[600px]">
      <div class="relative">
        <!-- Carousel -->
        <Carousel.Root
          opts={{ loop: false, dragFree: false }}
          setApi={handleCarouselApi}
          class="w-[510px]">
          <Carousel.Content>
            {#each steps as step}
              <Carousel.Item class="basis-full">
                <div class="flex flex-col items-center p-6 text-center">
                  <div
                    class="bg-primary/10 mb-4 flex h-16 w-16 items-center justify-center rounded-full">
                    {#if step.icon}
                      <step.icon class="text-primary h-8 w-8" />
                    {/if}
                  </div>
                  <h2 class="mb-2 text-2xl font-bold">{step.title}</h2>
                  <p class="text-muted-foreground mb-6 max-w-md">{step.description}</p>

                  <!-- Placeholder for image -->
                  <div
                    class="bg-muted mb-6 flex h-48 w-full items-center justify-center rounded-lg">
                    {#if step.icon}
                      <step.icon class="h-24 w-24 {step.color}" />
                    {/if}
                  </div>

                  <!-- Progress indicators -->
                  <div class="mb-6 flex gap-2">
                    {#each Array(totalSteps) as _, idx}
                      <div
                        class="h-2 w-2 rounded-full transition-colors duration-200"
                        class:bg-primary={idx === currentStep}
                        class:bg-muted={idx !== currentStep}>
                      </div>
                    {/each}
                  </div>

                  <!-- Don't show again checkbox -->
                  <div class="mb-6 flex items-center space-x-2">
                    <Checkbox.Root
                      checked={dontShowAgain}
                      onCheckedChange={(checked) => (dontShowAgain = checked)}
                      id="dont-show-again" />
                    <label
                      for="dont-show-again"
                      class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      Don't show this again
                    </label>
                  </div>

                  <!-- Navigation buttons -->
                  <div class="flex w-full justify-between">
                    <Button
                      variant="outline"
                      onclick={() => prevStep()}
                      disabled={currentStep === 0}>
                      <ChevronLeft class="mr-2 h-4 w-4" />
                      Previous
                    </Button>

                    <Button
                      variant={currentStep === totalSteps - 1 ? 'default' : 'outline'}
                      onclick={() =>
                        currentStep === totalSteps - 1 ? completeOnboarding() : nextStep()}>
                      {currentStep === totalSteps - 1 ? 'Get Started' : 'Next'}
                      {#if currentStep === totalSteps - 1}
                        <CheckCircle class="ml-2 h-4 w-4" />
                      {:else}
                        <ChevronRight class="ml-2 h-4 w-4" />
                      {/if}
                    </Button>
                  </div>
                </div>
              </Carousel.Item>
            {/each}
          </Carousel.Content>
        </Carousel.Root>
      </div>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
