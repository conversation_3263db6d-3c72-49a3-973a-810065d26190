<script lang="ts">
  import { cn } from '$lib/utils.js';

  let {
    class: className,
    value = $bindable(1),
    onValueChange = undefined,
    min = 0,
    max = 100,
    step = 1,
    ...restProps
  } = $props();

  // Handle value change
  function handleChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const newValue = parseInt(target.value);

    if (onValueChange) {
      onValueChange(newValue);
    }
  }
</script>

<div class={cn('relative w-full', className)} {...restProps}>
  <input
    type="range"
    bind:value
    oninput={handleChange}
    {min}
    {max}
    {step}
    class="bg-secondary focus-visible:ring-ring h-2 w-full cursor-pointer appearance-none rounded-full focus:outline-none focus-visible:ring-1" />
</div>
