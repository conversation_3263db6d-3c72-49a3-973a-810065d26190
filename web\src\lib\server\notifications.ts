/**
 * Notifications Helper
 *
 * This module provides a simplified interface for creating notifications.
 */

import { prisma } from '$lib/server/prisma';
import { getRedisClient } from '$lib/server/redis';

// Notification types
export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  SYSTEM = 'system',
  JOB = 'job',
  APPLICATION = 'application',
  RESUME = 'resume',
}

// Notification priorities
export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

// Notification data interface
export interface NotificationData {
  userId: string;
  title: string;
  message: string;
  url?: string;
  type?: NotificationType;
  priority?: NotificationPriority;
  data?: Record<string, any>;
  expiresAt?: Date;
}

/**
 * Create a notification for a user
 */
export async function createNotification(data: NotificationData): Promise<boolean> {
  try {
    // Handle system notifications (no user)
    if (data.userId === 'system') {
      console.log('Creating system notification (no user)');
      // Skip user preference check for system notifications
    } else {
      // Get user notification preferences
      const preferences = await prisma.notificationSettings.findUnique({
        where: { userId: data.userId },
      });

      // If user has disabled in-app notifications, don't send
      if (preferences && !preferences.browserEnabled) {
        console.log(`User ${data.userId} has disabled in-app notifications`);
        return false;
      }
    }

    // Create notification data
    const notificationData = {
      userId: data.userId,
      title: data.title,
      message: data.message,
      url: data.url,
      type: data.type || NotificationType.INFO,
      priority: data.priority || NotificationPriority.MEDIUM,
      metadata: data.data ? JSON.stringify(data.data) : null,
      expiresAt: data.expiresAt,
      read: false,
    };

    // Store notification in database
    let notification;

    // For system notifications, create a global notification
    if (data.userId === 'system') {
      notification = await prisma.notification.create({
        data: {
          ...notificationData,
          userId: null, // No specific user
          global: true, // Make it a global notification
        },
      });
      console.log(`Created global system notification in database with ID ${notification.id}`);
    } else {
      // Normal user notification
      notification = await prisma.notification.create({
        data: notificationData,
      });
      console.log(`Created user notification in database with ID ${notification.id}`);
    }

    // Send notification via Redis
    const redis = await getRedisClient();
    if (!redis) {
      console.error('Redis client not available');
      return false;
    }

    // Generate a unique request ID for this notification
    const requestId = `notification:${notification.id}:${Date.now()}`;

    // Publish to user-specific channel with the request ID
    const notificationMessage = {
      id: notification.id,
      title: notification.title,
      message: notification.message,
      url: notification.url,
      type: notification.type,
      timestamp: notification.createdAt.toISOString(),
      requestId: requestId,
    };

    console.log(`Publishing notification to Redis for user ${data.userId}:`, notificationMessage);

    // Publish to user-specific channel
    await redis.publish(`user:${data.userId}:notifications`, JSON.stringify(notificationMessage));

    // Also broadcast via WebSocket for immediate delivery
    try {
      const { broadcastMessage } = await import('$lib/server/websocket');
      broadcastMessage({
        type: 'notification',
        data: notificationMessage,
        timestamp: new Date().toISOString(),
      });
      console.log(`Broadcast notification via WebSocket for user ${data.userId}`);
    } catch (wsError) {
      console.error('Error broadcasting notification via WebSocket:', wsError);
    }

    return true;
  } catch (error) {
    console.error('Error creating notification:', error);
    return false;
  }
}
