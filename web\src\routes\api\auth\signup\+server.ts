import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types.js';
import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import * as bcrypt from 'bcryptjs';
import { updateUserPlan } from '$lib/server/auth.js';
import { randomBytes } from 'crypto';
import { RedisConnection } from '$lib/server/redis';

// Constants
const BASE_URL = process.env.PUBLIC_BASE_URL || 'http://localhost:5173';
const TEST_EMAIL = '<EMAIL>';
const EMAIL_QUEUE_KEY = 'email:queue';

export const POST: RequestHandler = async ({ request, cookies }) => {
  const { email, password, confirmPassword, termsAccepted, referralCode } = await request.json();

  // Validate input fields
  if (!email || !password || !confirmPassword || !termsAccepted) {
    return json({ message: 'All fields are required.' }, { status: 400 });
  }

  // Check if passwords match
  if (password !== confirmPassword) {
    return json({ message: 'Passwords do not match.' }, { status: 400 });
  }

  // Check if email already exists
  const existingUser = await prisma.user.findUnique({
    where: { email },
  });

  if (existingUser) {
    return json({ message: 'Email already in use.' }, { status: 400 });
  }

  // Hash the password before saving it
  const passwordHash = await bcrypt.hash(password, 10);

  // Validate referral code if provided
  let referrerId: string | null = null;
  if (referralCode?.trim()) {
    const referrer = await prisma.user.findUnique({
      where: { referralCode: referralCode.trim().toUpperCase() },
    });

    if (!referrer) {
      return json({ message: 'Invalid referral code.' }, { status: 400 });
    }

    referrerId = referrer.id;
  }

  // Generate a verification token
  const verificationToken = randomBytes(32).toString('hex');
  const verificationExpires = new Date(Date.now() + 1000 * 60 * 60 * 24); // 24 hours

  // Create a new user in the database
  const newUser = await prisma.user.create({
    data: {
      email,
      passwordHash, // Save the hashed password
      emailVerified: false,
      verificationToken,
      verificationExpires,
      referredById: referrerId,
      // Also store verification data in preferences JSON for backward compatibility
      preferences: {
        emailVerified: false,
        verificationToken,
        verificationExpires: verificationExpires.toISOString(),
      },
    },
  });

  // Create referral record if user was referred
  if (referrerId && referralCode) {
    await prisma.referral.create({
      data: {
        referrerId,
        referredId: newUser.id,
        referralCode: referralCode.toUpperCase(),
        status: 'pending',
      },
    });
  }

  // Assign the user to a plan (e.g., 'free' plan) and create a subscription
  const planId = 'free'; // Default to 'free' plan
  const seats = 1; // Default to 1 seat
  await updateUserPlan(newUser.email, planId, seats);

  // Store verification data in preferences
  console.log(`User created with verification token: ${verificationToken}`);
  console.log(`Verification URL: ${BASE_URL}/auth/verify?token=${verificationToken}`);

  // Log verification URL for testing
  const verificationUrl = `${BASE_URL}/auth/verify?token=${verificationToken}`;
  console.log(`Verification URL for ${email}: ${verificationUrl}`);

  // Queue verification email using Redis
  try {
    console.log('RedisConnection:', RedisConnection ? 'Available' : 'Not available');
    if (RedisConnection) {
      console.log('RedisConnection status:', RedisConnection.status);
    }

    if (!RedisConnection) {
      console.error('Redis client not available');
      // Continue with account creation even if Redis is not available
    } else {
      // Generate a unique ID for this email
      const id = `email_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

      // Create the email job for the user
      const emailJob = {
        id,
        template: 'verification',
        to: [email],
        data: {
          firstName: email.split('@')[0],
          verificationUrl,
          expiresInMinutes: 60 * 24, // 24 hours
        },
        options: {
          category: 'transactional',
          priority: 1, // Highest priority
          retries: 3,
          tags: [{ name: 'action', value: 'verification' }],
        },
        createdAt: new Date().toISOString(),
      };

      // Add to Redis queue with priority
      console.log('Adding email job to Redis queue:', EMAIL_QUEUE_KEY);
      await RedisConnection.zadd(
        EMAIL_QUEUE_KEY,
        1, // Highest priority
        JSON.stringify(emailJob)
      );
      console.log(`Verification email queued in Redis: ${id} (to ${email})`);

      // Only send a test copy if explicitly enabled and not sending to the test email already
      const SEND_TEST_COPIES = process.env.SEND_TEST_EMAIL_COPIES === 'true';

      if (SEND_TEST_COPIES && email.toLowerCase() !== TEST_EMAIL.toLowerCase()) {
        // Create the email job for test purposes
        const testEmailId = `email_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

        const testEmailJob = {
          id: testEmailId,
          template: 'verification',
          to: [TEST_EMAIL],
          data: {
            firstName: 'Test Copy',
            verificationUrl,
            expiresInMinutes: 60 * 24, // 24 hours
          },
          options: {
            category: 'transactional',
            priority: 1, // Highest priority
            retries: 3,
            tags: [
              { name: 'action', value: 'verification' },
              { name: 'test', value: 'true' },
            ],
          },
          createdAt: new Date().toISOString(),
        };

        // Add to Redis queue with priority
        await RedisConnection.zadd(
          EMAIL_QUEUE_KEY,
          1, // Highest priority
          JSON.stringify(testEmailJob)
        );
        console.log(`Test verification email queued in Redis: ${testEmailId} (to ${TEST_EMAIL})`);
      }

      // Trigger immediate processing by publishing a message
      await RedisConnection.publish('email:process', 'process_now');
      console.log('Published process_now message to email:process channel');
    }
  } catch (error) {
    console.error('Failed to queue verification email:', error);
    // Continue with account creation even if email fails
  }

  // Don't create a session token until the email is verified
  // We'll create the token when the user verifies their email

  return json(
    {
      message: 'Account created successfully! Please check your email to verify your account.',
      verified: false,
      // Include the verification token for testing purposes
      verificationToken: verificationToken,
      verificationUrl: `${BASE_URL}/auth/verify?token=${verificationToken}`,
    },
    { status: 201 }
  );
};
