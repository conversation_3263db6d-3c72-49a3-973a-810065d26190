import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types.js';
import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { createSessionToken } from '$lib/server/auth.js';
import { getRedisClient } from '$lib/server/redis';

// Constants
const BASE_URL = process.env.PUBLIC_BASE_URL || 'http://localhost:5173';
const TEST_EMAIL = '<EMAIL>';
const EMAIL_QUEUE_KEY = 'email:queue';

export const GET: RequestHandler = async ({ url, cookies }) => {
  const token = url.searchParams.get('token');

  if (!token) {
    return json({ error: 'Verification token is required' }, { status: 400 });
  }

  // Try to find the user with the verification token directly
  let user = await prisma.user.findFirst({
    where: {
      verificationToken: token,
      verificationExpires: {
        gt: new Date(),
      },
    },
    // TODO: Add referredById to select once Prisma client is regenerated
    select: {
      id: true,
      email: true,
      name: true,
      emailVerified: true,
      verificationToken: true,
      verificationExpires: true,
      preferences: true,
      // referredById: true,
    },
  });

  // If not found, try to find in preferences (for backward compatibility)
  if (!user) {
    const users = await prisma.user.findMany();

    // Find the user with this verification token in preferences
    user = users.find((u) => {
      try {
        // Handle both string and object preferences
        const prefs = typeof u.preferences === 'string' ? JSON.parse(u.preferences) : u.preferences;

        return (
          prefs.verificationToken === token && new Date(prefs.verificationExpires) > new Date()
        );
      } catch (e) {
        console.error('Error parsing preferences:', e);
        return false;
      }
    });
  }

  if (!user) {
    return json({ error: 'Invalid or expired verification token' }, { status: 400 });
  }

  // Mark the user as verified
  try {
    // Handle both string and object preferences
    let prefs: Record<string, any> = {};
    if (typeof user.preferences === 'string') {
      prefs = JSON.parse(user.preferences) || {};
    } else if (user.preferences && typeof user.preferences === 'object') {
      prefs = { ...(user.preferences as Record<string, any>) };
    }

    // Update preferences
    prefs.emailVerified = true;
    delete prefs.verificationToken;
    delete prefs.verificationExpires;

    // Update both the emailVerified field and preferences
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: true,
        verificationToken: null,
        verificationExpires: null,
        preferences: prefs,
      },
    });
  } catch (error) {
    console.error('Failed to update user verification status:', error);
    return json({ error: 'Failed to verify email' }, { status: 500 });
  }

  // TODO: Complete referral if user was referred (uncomment after Prisma client regeneration)
  /*
  if (user.referredById) {
    try {
      // Update referral status to completed
      const updatedReferrals = await prisma.referral.updateMany({
        where: {
          referredId: user.id,
          referrerId: user.referredById,
          status: 'pending',
        },
        data: {
          status: 'completed',
          completedAt: new Date(),
        },
      });

      // Only increment count if we actually updated a referral
      if (updatedReferrals.count > 0) {
        await prisma.user.update({
          where: { id: user.referredById },
          data: {
            referralCount: {
              increment: 1,
            },
          },
        });

        console.log(`Referral completed for user ${user.id} referred by ${user.referredById}`);
      } else {
        console.log(`No pending referral found for user ${user.id}`);
      }
    } catch (error) {
      console.error('Error completing referral:', error);
      // Don't fail verification if referral update fails
    }
  }
  */

  // Log welcome message
  console.log(`User ${user.email} has been verified successfully`);

  // Queue welcome email using Redis
  try {
    // Get Redis client
    const redis = await getRedisClient();

    if (!redis) {
      console.error('Redis client not available');
      // Continue with verification even if Redis is not available
    } else {
      // Generate a unique ID for this email
      const id = `email_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

      const loginUrl = `${BASE_URL}/auth/sign-in`;
      const dashboardUrl = `${BASE_URL}/dashboard`;

      // Create the email job for the user
      const emailJob = {
        id,
        template: 'welcome',
        to: [user.email],
        data: {
          firstName: user.name || user.email.split('@')[0],
          loginUrl,
          dashboardUrl,
        },
        options: {
          category: 'transactional',
          priority: 2, // High priority
          retries: 3,
          tags: [{ name: 'action', value: 'welcome' }],
        },
        createdAt: new Date().toISOString(),
      };

      // Add to Redis queue with priority
      await redis.zadd(
        EMAIL_QUEUE_KEY,
        2, // High priority
        JSON.stringify(emailJob)
      );

      console.log(`Welcome email queued in Redis: ${id} (to ${user.email})`);

      // Also send a copy to Christopher for testing if not already sending to him
      if (user.email.toLowerCase() !== TEST_EMAIL.toLowerCase()) {
        // Create the email job for Christopher
        const testEmailId = `email_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

        const testEmailJob = {
          id: testEmailId,
          template: 'welcome',
          to: [TEST_EMAIL],
          data: {
            firstName: 'Christopher (Test Copy)',
            loginUrl,
            dashboardUrl,
          },
          options: {
            category: 'transactional',
            priority: 2, // High priority
            retries: 3,
            tags: [
              { name: 'action', value: 'welcome' },
              { name: 'test', value: 'true' },
            ],
          },
          createdAt: new Date().toISOString(),
        };

        // Add to Redis queue with priority
        await redis.zadd(
          EMAIL_QUEUE_KEY,
          2, // High priority
          JSON.stringify(testEmailJob)
        );

        console.log(`Test welcome email queued in Redis: ${testEmailId} (to ${TEST_EMAIL})`);
      }

      // Trigger immediate processing by publishing a message
      await redis.publish('email:process', 'process_now');
    }
  } catch (error) {
    console.error('Failed to queue welcome email:', error);
    // Continue with verification even if email fails
  }

  // Don't create a session token here - let the user sign in
  // This ensures they have to enter their password after verification

  // Redirect to sign-in page with verified parameter
  return new Response(null, {
    status: 302,
    headers: {
      Location: `${BASE_URL}/auth/sign-in?verified=true&email=${encodeURIComponent(user.email)}`,
    },
  });
};
