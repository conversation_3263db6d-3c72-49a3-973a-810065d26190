import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth';

// Generate a unique referral code
function generateReferralCode(name?: string, email?: string): string {
  const prefix = name 
    ? name.replace(/[^a-zA-Z]/g, '').substring(0, 3).toUpperCase()
    : email?.substring(0, 3).toUpperCase() || 'REF';
  
  const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `${prefix}${randomSuffix}`;
}

// GET - Get user's referral information
export const GET: RequestHandler = async ({ cookies }) => {
  const token = cookies.get('auth_token');
  if (!token) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const userData = verifySessionToken(token);
    if (!userData?.id) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // Get user with referral data
    const user = await prisma.user.findUnique({
      where: { id: userData.id },
      include: {
        referrals: {
          include: {
            referred: {
              select: {
                id: true,
                name: true,
                email: true,
                createdAt: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        referralsMade: {
          include: {
            referred: {
              select: {
                id: true,
                name: true,
                email: true,
                createdAt: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        referredBy: {
          select: {
            id: true,
            name: true,
            email: true,
            referralCode: true,
          },
        },
      },
    });

    if (!user) {
      return json({ error: 'User not found' }, { status: 404 });
    }

    // Generate referral code if user doesn't have one
    let referralCode = user.referralCode;
    if (!referralCode) {
      referralCode = generateReferralCode(user.name, user.email);
      
      // Ensure uniqueness
      let attempts = 0;
      while (attempts < 5) {
        const existing = await prisma.user.findUnique({
          where: { referralCode },
        });
        
        if (!existing) break;
        
        referralCode = generateReferralCode(user.name, user.email);
        attempts++;
      }

      // Update user with referral code
      await prisma.user.update({
        where: { id: user.id },
        data: { referralCode },
      });
    }

    const baseUrl = process.env.PUBLIC_BASE_URL || 'http://localhost:5173';
    const referralLink = `${baseUrl}/auth/sign-up?ref=${referralCode}`;

    return json({
      referralCode,
      referralLink,
      referralCount: user.referralCount,
      referralRewards: user.referralRewards,
      referrals: user.referralsMade,
      referredBy: user.referredBy,
    });
  } catch (error) {
    console.error('Error getting referral data:', error);
    return json({ error: 'Failed to get referral data' }, { status: 500 });
  }
};

// POST - Create or update referral code
export const POST: RequestHandler = async ({ request, cookies }) => {
  const token = cookies.get('auth_token');
  if (!token) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const userData = verifySessionToken(token);
    if (!userData?.id) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const { action, customCode } = await request.json();

    if (action === 'regenerate' || action === 'create') {
      const user = await prisma.user.findUnique({
        where: { id: userData.id },
      });

      if (!user) {
        return json({ error: 'User not found' }, { status: 404 });
      }

      let newReferralCode: string;
      
      if (customCode && customCode.length >= 4 && customCode.length <= 12) {
        // Validate custom code
        if (!/^[A-Za-z0-9]+$/.test(customCode)) {
          return json({ error: 'Referral code can only contain letters and numbers' }, { status: 400 });
        }
        
        // Check if custom code is available
        const existing = await prisma.user.findUnique({
          where: { referralCode: customCode.toUpperCase() },
        });
        
        if (existing && existing.id !== userData.id) {
          return json({ error: 'This referral code is already taken' }, { status: 400 });
        }
        
        newReferralCode = customCode.toUpperCase();
      } else {
        newReferralCode = generateReferralCode(user.name, user.email);
        
        // Ensure uniqueness
        let attempts = 0;
        while (attempts < 5) {
          const existing = await prisma.user.findUnique({
            where: { referralCode: newReferralCode },
          });
          
          if (!existing) break;
          
          newReferralCode = generateReferralCode(user.name, user.email);
          attempts++;
        }
      }

      // Update user with new referral code
      await prisma.user.update({
        where: { id: userData.id },
        data: { referralCode: newReferralCode },
      });

      const baseUrl = process.env.PUBLIC_BASE_URL || 'http://localhost:5173';
      const referralLink = `${baseUrl}/auth/sign-up?ref=${newReferralCode}`;

      return json({
        referralCode: newReferralCode,
        referralLink,
        message: 'Referral code updated successfully',
      });
    }

    return json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Error updating referral code:', error);
    return json({ error: 'Failed to update referral code' }, { status: 500 });
  }
};
