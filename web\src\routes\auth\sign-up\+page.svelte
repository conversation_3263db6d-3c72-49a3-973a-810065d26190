<script lang="ts">
  import { page } from '$app/stores';
  import { browser } from '$app/environment';

  let email = '';
  let password = '';
  let confirmPassword = '';
  let isLoading = false;
  let termsAccepted = false; // Track if terms are accepted
  let showVerificationForm = false; // To show verification form after sign-up
  let referralCode = ''; // Track referral code
  let referrerName = ''; // Track referrer name for display
  let validatingReferral = false;

  // Password matching and requirements checks
  let passwordValid = false;
  let passwordsMatch = false;
  let showPassword = false;
  let showConfirmPassword = false;

  // Password requirements validation states
  let hasUpperCase = false;
  let hasSpecialChar = false;
  let minLength = false;
  let passwordFocused = false; // To track if the password input is focused
  // Track if the password has been modified or not
  let emailValid = false;
  let emailTouched = false; // To track if email field has been focused

  import Button from '$lib/components/ui/button/button.svelte';
  import Input from '$lib/components/ui/input/input.svelte';
  import Checkbox from '$lib/components/ui/checkbox/checkbox.svelte';
  import { toast } from 'svelte-sonner';
  import { signIn } from '@auth/sveltekit/client';
  import SEO from '$components/shared/SEO.svelte';

  // Email Validation logic
  function validateEmail() {
    // Simple regex to check if the email follows the correct format
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    emailValid = emailRegex.test(email);
  }

  // Handle focus state when typing in the email field
  function handleEmailFocus() {
    emailTouched = true;
  }

  // Handle blur state when the user moves out of the email field
  function handleEmailBlur() {
    emailTouched = true;
  }

  // Check for referral code in URL on component mount
  $: if (browser && typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search);
    const refCode = urlParams.get('ref');
    if (refCode && !referralCode) {
      referralCode = refCode;
      validateReferralCode(refCode);
    }
  }

  // Validate referral code
  const validateReferralCode = async (code: string) => {
    if (!code) return;

    validatingReferral = true;
    try {
      const response = await fetch(`/api/referrals/validate?code=${encodeURIComponent(code)}`);
      const data = await response.json();

      if (data.valid) {
        referrerName = data.referrer.name || 'Someone';
        toast.success('Referral Code Valid', {
          description: `You were referred by ${referrerName}!`,
        });
      } else {
        referralCode = '';
        toast.error('Invalid Referral Code', {
          description: 'The referral code you entered is not valid.',
        });
      }
    } catch (error) {
      console.error('Error validating referral code:', error);
      referralCode = '';
      toast.error('Error', {
        description: 'Failed to validate referral code.',
      });
    } finally {
      validatingReferral = false;
    }
  };

  // Sign Up with email/password
  const signUp = async () => {
    isLoading = true;
    const res = await fetch('/api/auth/signup', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password, confirmPassword, termsAccepted, referralCode }),
    });

    isLoading = false;

    if (res.ok) {
      const data = await res.json();

      // Show success toast with verification instructions
      toast.success('Account Created', {
        description:
          'Please check your email for a verification link to complete your registration.',
        duration: 10000,
      });

      // Show verification form
      showVerificationForm = true;

      // Log verification info for testing
      console.log('Account created successfully!');
      console.log('Verification Token:', data.verificationToken);
      console.log('Verification URL:', data.verificationUrl);
    } else {
      const { message } = await res.json();
      toast.error('Error', {
        description: message || 'An error occurred while creating the account.',
      });
      console.error(message); // Show the error message from the backend
    }
  };

  // Verification is now handled via email link

  // Password Validation logic
  function validatePassword() {
    // Check for password requirements
    hasUpperCase = /[A-Z]/.test(password); // At least one uppercase letter
    hasSpecialChar = /[!@#$%^&*]/.test(password); // At least one special character
    minLength = password.length >= 8; // Minimum 8 characters

    // Check if password and confirmPassword match
    passwordsMatch = password === confirmPassword;
    passwordValid = hasUpperCase && hasSpecialChar && minLength;

    if (passwordValid) {
      passwordFocused = false;
    }
  }

  // Set focus state when typing in the password field
  function handlePasswordFocus() {
    passwordFocused = true;
  }

  function handlePasswordBlur() {
    if (passwordValid) {
      passwordFocused = false;
    }
  }

  // Toggle show password
  function togglePassword() {
    showPassword = !showPassword;
  }

  // Toggle show confirm password
  function toggleConfirmPassword() {
    showConfirmPassword = !showConfirmPassword;
  }
</script>

<SEO
  title="Sign Up | Hirli"
  description="Create your Hirli account to automate your job applications and streamline your job search process."
  keywords="sign up, create account, job application, career, automation, Hirli account" />

<!-- Sign Up Form Section -->
<div class="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
  <div class="mb-8 flex flex-col space-y-2 text-left">
    <h1 class="text-3xl font-semibold tracking-tight">Sign up</h1>
    <p class="text-muted-foreground text-md font-light">
      Already have an account? <a class="underline" href="/auth/sign-in">Sign in</a>
    </p>
  </div>

  {#if !showVerificationForm}
    <!-- OAuth buttons - Outside the form to avoid nesting forms -->
    <div class="grid gap-6">
      <div class="flex flex-col items-center justify-center gap-4">
        <!-- Google Login Button -->
        <Button
          type="button"
          class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex w-full items-center justify-center whitespace-nowrap rounded-md border p-4 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"
          on:click={signIn('google', { callbackUrl: '/dashboard' })}>
          <img alt="Google" src="/assets/svg/google.svg" class="mr-2" />
          Sign up with Google
        </Button>

        <!-- LinkedIn Button -->
        <Button
          type="button"
          class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex w-full items-center justify-center whitespace-nowrap rounded-md border p-4 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"
          on:click={signIn('linkedin', { callbackUrl: '/dashboard' })}>
          <img alt="LinkedIn" src="/assets/svg/linkedin.svg" class="mr-2" />
          Sign up with LinkedIn
        </Button>
      </div>

      <!-- Divider -->
      <div class="relative">
        <div class="absolute inset-0 flex items-center">
          <span class="border-border w-full border-2 border-t"></span>
        </div>
        <div class="relative z-10 flex justify-center text-xs uppercase">
          <span class="text-muted-foreground bg-background px-2">Or</span>
        </div>
      </div>
    </div>

    <!-- Email/Password Sign Up Form -->
    <form class="grid gap-6" on:submit|preventDefault={signUp}>
      <!-- Email Input -->
      <div class="grid gap-2">
        <Input
          id="email"
          type="email"
          required
          bind:value={email}
          placeholder="<EMAIL>"
          class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm"
          on:input={validateEmail}
          on:focus={handleEmailFocus}
          on:blur={handleEmailBlur} />
        {#if emailTouched && !emailValid}
          <p class="text-destructive text-sm">Please enter a valid email address.</p>
        {/if}
      </div>

      <!-- Password Input -->
      <div class="relative">
        <Input
          id="password"
          type={showPassword ? 'text' : 'password'}
          bind:value={password}
          placeholder="Password"
          disabled={isLoading}
          on:input={validatePassword}
          on:focus={handlePasswordFocus}
          on:blur={handlePasswordBlur}
          class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border {passwordValid
            ? 'border-green-500'
            : 'border-destructive'} bg-transparent px-3 py-1 text-sm shadow-sm" />
        <button
          type="button"
          on:click={togglePassword}
          class="absolute right-0 top-0.5 p-2"
          aria-label={showPassword ? 'Hide password' : 'Show password'}>
          <i class={`fa-solid ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
        </button>
      </div>

      <!-- Password Requirements with Bullet Points -->
      {#if passwordFocused && !passwordValid}
        <div class="mt-2 rounded-md border p-2">
          <ul class="space-y-2 text-sm">
            <li class={minLength ? 'text-green-500' : 'text-destructive'}>
              &#8226; Minimum 8 characters
            </li>
            <li class={hasUpperCase ? 'text-green-500' : 'text-destructive'}>
              &#8226; At least one uppercase letter
            </li>
            <li class={hasSpecialChar ? 'text-green-500' : 'text-destructive'}>
              &#8226; At least one special character (!, @, #, $, etc.)
            </li>
          </ul>
        </div>
      {/if}

      <!-- Confirm Password Input -->
      <div class="relative">
        <Input
          id="confirm-password"
          type={showConfirmPassword ? 'text' : 'password'}
          bind:value={confirmPassword}
          placeholder="Confirm Password"
          disabled={isLoading}
          on:input={validatePassword}
          class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border {passwordsMatch
            ? 'border-green-500'
            : 'border-destructive'} bg-transparent px-3 py-1 text-sm shadow-sm" />
        <button
          type="button"
          on:click={toggleConfirmPassword}
          class="absolute right-0 top-0.5 p-2"
          aria-label={showConfirmPassword ? 'Hide confirm password' : 'Show confirm password'}>
          <i class={`fa-solid ${showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
        </button>
      </div>
      {#if !passwordsMatch && confirmPassword !== ''}
        <p class="text-destructive text-sm">Passwords do not match.</p>
      {/if}

      <!-- Referral Code Input -->
      <div class="grid gap-2">
        <label for="referralCode" class="text-sm font-medium">
          Referral Code <span class="text-muted-foreground">(Optional)</span>
        </label>
        <Input
          id="referralCode"
          type="text"
          bind:value={referralCode}
          placeholder="Enter referral code"
          class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm"
          on:input={() => {
            if (referralCode) {
              validateReferralCode(referralCode);
            }
          }} />
        {#if referrerName}
          <p class="text-sm text-green-600">✓ Referred by {referrerName}</p>
        {/if}
        {#if validatingReferral}
          <p class="text-muted-foreground text-sm">Validating referral code...</p>
        {/if}
      </div>

      <!-- Terms and Conditions Checkbox -->
      <div class="flex items-center space-x-4">
        <Checkbox bind:checked={termsAccepted} />
        <label for="terms" class="text-sm">
          Agree to our
          <a href="/legal/terms" class="hover:text-primary underline underline-offset-4">
            Terms of Service
          </a>
          and
          <a href="/legal/privacy" class="hover:text-primary underline underline-offset-4">
            Privacy Policy
          </a>.
        </label>
      </div>

      <!-- Submit Button -->
      <Button
        class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"
        type="submit"
        disabled={isLoading ||
          !passwordValid ||
          !passwordsMatch ||
          !minLength ||
          !hasUpperCase ||
          !hasSpecialChar ||
          !termsAccepted ||
          !email}>
        {isLoading ? 'Creating...' : 'Sign Up'}
      </Button>
    </form>
  {:else}
    <!-- Verification Form -->
    <div class="grid gap-6">
      <div class="border-border bg-primary/10 text-primary rounded-lg border p-4 text-sm">
        <p>
          We've sent a verification link to <strong>{email}</strong>. Please check your email and
          click the link to verify your account.
        </p>
      </div>

      <div class="mt-4 flex flex-col items-center justify-center gap-4">
        <p class="text-muted-foreground text-center text-sm">
          Didn't receive the email? Check your spam folder or try again.
        </p>

        <!-- Back Button -->
        <Button
          class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"
          type="button"
          onclick={() => {
            // Reset the form to show the sign-up form again
            showVerificationForm = false;
          }}>
          Back to Sign Up
        </Button>
      </div>
    </div>
  {/if}
</div>
