<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card/index.js';
  import SEO from '$components/shared/SEO.svelte';
  import { CheckCircle } from 'lucide-svelte';
</script>

<SEO title="Email Verified | Auto Apply" description="Your email has been verified successfully" />

<div class="flex min-h-[calc(100vh-200px)] flex-col items-center justify-center p-4">
  <div class="w-full max-w-md">
    <Card.Root class="text-center">
      <Card.Content class="pt-6">
        <div class="mb-4 flex justify-center">
          <div class="rounded-full bg-green-100 p-3">
            <CheckCircle class="h-10 w-10 text-green-600" />
          </div>
        </div>

        <h1 class="mb-2 text-3xl font-bold">Email Verified!</h1>

        <p class="text-muted-foreground mb-6 max-w-md">
          Your email address has been successfully verified. You can now access all features of your
          account.
        </p>

        <div class="space-y-3">
          <Button href="/auth/sign-in" class="w-full">Sign In to Your Account</Button>
          <Button href="/" variant="outline" class="w-full">Go to Homepage</Button>
        </div>
      </Card.Content>
    </Card.Root>
  </div>
</div>
