<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import SEO from '$components/shared/SEO.svelte';
  import { Button } from '$lib/components/ui/button/index.js';
  import * as Card from '$lib/components/ui/card/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { CheckCircle, XCircle, Loader2, Gift, Users } from 'lucide-svelte';

  let isLoading = true;
  let error = '';
  let success = false;
  let referralCompleted = false;
  let referrerName = '';
  let userEmail = '';

  onMount(async () => {
    const token = $page.url.searchParams.get('token');

    if (!token) {
      error = 'Verification token is missing.';
      isLoading = false;
      return;
    }

    try {
      // Call the verification API
      const response = await fetch(`/api/auth/verify?token=${token}`);

      // If the response is a redirect, the verification was successful
      if (response.redirected) {
        // Extract email from redirect URL to check for referral info
        const redirectUrl = new URL(response.url);
        userEmail = redirectUrl.searchParams.get('email') || '';

        // Check if this user was referred and if referral was completed
        if (userEmail) {
          await checkReferralStatus(userEmail);
        }

        // Show success state briefly before redirecting
        success = true;
        isLoading = false;

        // Redirect after showing success message
        setTimeout(() => {
          window.location.href = '/auth/sign-in?verified=true';
        }, 3000);
        return;
      }

      // Otherwise, check the response for errors
      const data = await response.json();

      if (!response.ok) {
        error = data.error || 'Failed to verify email.';
      } else {
        success = true;
        // Check for referral completion if we have user data
        if (data.email) {
          userEmail = data.email;
          await checkReferralStatus(data.email);
        }

        // Redirect after showing success message
        setTimeout(() => {
          window.location.href = '/auth/sign-in?verified=true';
        }, 3000);
      }
    } catch (err) {
      console.error('Verification error:', err);
      error = 'An error occurred during verification.';
    } finally {
      isLoading = false;
    }
  });

  // Check if referral was completed for this user
  async function checkReferralStatus(email: string) {
    try {
      // Get user info to check referral status
      const response = await fetch('/api/referrals');
      if (response.ok) {
        const data = await response.json();
        if (data.referredBy) {
          referralCompleted = true;
          referrerName = data.referredBy.name || data.referredBy.email;
        }
      }
    } catch (error) {
      console.error('Error checking referral status:', error);
      // Don't show error for referral check failure
    }
  }
</script>

<SEO title="Verify Email | Auto Apply" description="Verify your email address" />

<div class="flex min-h-[calc(100vh-200px)] flex-col items-center justify-center p-4">
  <div class="w-full max-w-md">
    {#if isLoading}
      <Card.Root class="text-center">
        <Card.Content class="pt-6">
          <div class="mb-4 flex justify-center">
            <Loader2 class="text-primary h-12 w-12 animate-spin" />
          </div>
          <h1 class="mb-2 text-2xl font-bold">Verifying Your Email</h1>
          <p class="text-muted-foreground">Please wait while we verify your email address...</p>
        </Card.Content>
      </Card.Root>
    {:else if error}
      <Card.Root class="text-center">
        <Card.Content class="pt-6">
          <div class="mb-4 flex justify-center">
            <div class="bg-destructive/10 rounded-full p-3">
              <XCircle class="text-destructive h-10 w-10" />
            </div>
          </div>
          <h1 class="mb-2 text-2xl font-bold">Verification Failed</h1>
          <p class="text-muted-foreground mb-6">{error}</p>
          <div class="flex flex-col gap-3 sm:flex-row">
            <Button href="/auth/sign-in" class="flex-1">Go to Sign In</Button>
            <Button href="/" variant="outline" class="flex-1">Go to Homepage</Button>
          </div>
        </Card.Content>
      </Card.Root>
    {:else if success}
      <Card.Root class="text-center">
        <Card.Content class="pt-6">
          <div class="mb-4 flex justify-center">
            <div class="rounded-full bg-green-100 p-3">
              <CheckCircle class="h-10 w-10 text-green-600" />
            </div>
          </div>
          <h1 class="mb-2 text-2xl font-bold">Email Verified!</h1>
          <p class="text-muted-foreground mb-4">
            Your email has been successfully verified. You can now access all features of your
            account.
          </p>

          <!-- Referral Completion Message -->
          {#if referralCompleted && referrerName}
            <div class="mb-6 rounded-lg border border-green-200 bg-green-50 p-4">
              <div class="mb-2 flex items-center justify-center gap-2">
                <Gift class="h-5 w-5 text-green-600" />
                <span class="font-semibold text-green-800">Referral Bonus!</span>
              </div>
              <p class="text-sm text-green-700">
                Thanks for joining through <strong>{referrerName}</strong>'s referral! Your referral
                has been completed and both you and {referrerName} may be eligible for rewards.
              </p>
              <div class="mt-2 flex items-center justify-center gap-1">
                <Users class="h-4 w-4 text-green-600" />
                <Badge variant="secondary" class="text-xs">Referral Completed</Badge>
              </div>
            </div>
          {/if}

          <div class="space-y-3">
            <Button href="/auth/sign-in" class="w-full">Sign In to Your Account</Button>
            <Button href="/" variant="outline" class="w-full">Go to Homepage</Button>
          </div>

          <div class="text-muted-foreground mt-4 text-sm">
            Redirecting to sign in page in a few seconds...
          </div>
        </Card.Content>
      </Card.Root>
    {/if}
  </div>
</div>
