<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog';
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import * as Select from '$lib/components/ui/select';
  import { Switch } from '$lib/components/ui/switch';
  import { toast } from 'svelte-sonner';
  import { createForm } from 'svelte-forms-lib';
  import * as yup from 'yup';

  export let onClose: () => void;
  export let onCreated: () => void;
  export const userId: string = '';

  // Form validation schema
  const schema = yup.object().shape({
    name: yup.string().required('Alert name is required'),
    keywords: yup.string(),
    location: yup.string(),
    jobType: yup.string(),
    remote: yup.boolean(),
    frequency: yup.string().required('Frequency is required'),
    enabled: yup.boolean(),
  });

  // Initialize form
  const { form, errors, handleSubmit, isSubmitting } = createForm({
    initialValues: {
      name: '',
      keywords: '',
      location: '',
      jobType: '',
      remote: false,
      frequency: 'daily',
      enabled: true,
    },
    validationSchema: schema,
    onSubmit: async (values) => {
      try {
        // Create search params object
        const searchParams = {
          keywords: values.keywords || undefined,
          location: values.location || undefined,
          jobType: values.jobType || undefined,
          remote: values.remote || undefined,
        };

        // Remove undefined values
        Object.keys(searchParams).forEach((key) => {
          if (searchParams[key] === undefined) {
            delete searchParams[key];
          }
        });

        // Create alert data
        const alertData = {
          name: values.name,
          searchParams,
          frequency: values.frequency,
          enabled: values.enabled,
        };

        // Call API to create alert
        const response = await fetch('/api/job-alerts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(alertData),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to create job alert');
        }

        toast.success('Job alert created successfully');
        onCreated();
      } catch (error) {
        console.error('Error creating job alert:', error);
        toast.error('Failed to create job alert');
      }
    },
  });

  // Job type options
  const jobTypeOptions = [
    { value: '', label: 'Any' },
    { value: 'full_time', label: 'Full-time' },
    { value: 'part_time', label: 'Part-time' },
    { value: 'contract', label: 'Contract' },
    { value: 'temporary', label: 'Temporary' },
    { value: 'internship', label: 'Internship' },
  ];

  // Frequency options
  const frequencyOptions = [
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
  ];
</script>

<Dialog.Root open={true}>
  <Dialog.Overlay />
  <Dialog.Content class="sm:max-w-[500px]">
    <Dialog.Header>
      <Dialog.Title>Create Job Alert</Dialog.Title>
      <Dialog.Description>
        Set up a new job alert to get notified when new jobs matching your criteria are available.
        You'll receive email notifications based on your selected frequency.
      </Dialog.Description>
    </Dialog.Header>

    <div class="mb-2 rounded-lg bg-blue-50 p-3 text-sm text-blue-800">
      <div class="flex items-start">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="mr-2 mt-0.5 h-5 w-5 text-blue-600"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="16" x2="12" y2="12"></line>
          <line x1="12" y1="8" x2="12.01" y2="8"></line>
        </svg>
        <div>
          <p class="font-medium">Tips for effective job alerts:</p>
          <ul class="ml-5 mt-1 list-disc">
            <li>Use specific keywords related to your skills</li>
            <li>Include location preferences or select "Remote"</li>
            <li>Choose a frequency that works best for your job search</li>
          </ul>
        </div>
      </div>
    </div>

    <form on:submit|preventDefault={handleSubmit}>
      <div class="grid gap-4 py-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <Label for="name" class="text-right">Alert Name</Label>
          <div class="col-span-3">
            <Input
              id="name"
              placeholder="e.g., Software Developer Jobs"
              bind:value={$form.name}
              class={$errors.name ? 'border-red-500' : ''} />
            {#if $errors.name}
              <p class="mt-1 text-xs text-red-500">{$errors.name}</p>
            {/if}
          </div>
        </div>

        <div class="grid grid-cols-4 items-center gap-4">
          <Label for="keywords" class="text-right">Keywords</Label>
          <div class="col-span-3">
            <Input
              id="keywords"
              placeholder="e.g., javascript, react"
              bind:value={$form.keywords} />
          </div>
        </div>

        <div class="grid grid-cols-4 items-center gap-4">
          <Label for="location" class="text-right">Location</Label>
          <div class="col-span-3">
            <Input id="location" placeholder="e.g., New York, Remote" bind:value={$form.location} />
          </div>
        </div>

        <div class="grid grid-cols-4 items-center gap-4">
          <Label for="jobType" class="text-right">Job Type</Label>
          <div class="col-span-3">
            <Select.Root
              type="single"
              name="jobType"
              value={$form.jobType}
              onValueChange={(value) => {
                $form.jobType = value;
              }}>
              <Select.Trigger class="w-full">
                <Select.Value placeholder="Select job type" />
              </Select.Trigger>
              <Select.Content class="max-h-60">
                {#each jobTypeOptions as option}
                  <Select.Item value={option.value}>{option.label}</Select.Item>
                {/each}
              </Select.Content>
            </Select.Root>
          </div>
        </div>

        <div class="grid grid-cols-4 items-center gap-4">
          <Label for="remote" class="text-right">Remote Only</Label>
          <div class="col-span-3 flex items-center space-x-2">
            <Switch
              id="remote"
              name="remote"
              checked={$form.remote}
              onCheckedChange={(checked) => {
                $form.remote = checked;
              }} />
            <span class="text-muted-foreground text-sm">Only show remote jobs</span>
          </div>
        </div>

        <div class="grid grid-cols-4 items-center gap-4">
          <Label for="frequency" class="text-right">Frequency</Label>
          <div class="col-span-3">
            <Select.Root
              type="single"
              name="frequency"
              value={$form.frequency}
              onValueChange={(value: string) => {
                $form.frequency = value;
              }}>
              <Select.Trigger class="w-full">
                <Select.Value placeholder="Select frequency" />
              </Select.Trigger>
              <Select.Content class="max-h-60">
                {#each frequencyOptions as option}
                  <Select.Item value={option.value}>{option.label}</Select.Item>
                {/each}
              </Select.Content>
            </Select.Root>
            {#if $errors.frequency}
              <p class="mt-1 text-xs text-red-500">{$errors.frequency}</p>
            {/if}
          </div>
        </div>

        <div class="grid grid-cols-4 items-center gap-4">
          <Label for="enabled" class="text-right">Enabled</Label>
          <div class="col-span-3 flex items-center space-x-2">
            <Switch
              id="enabled"
              name="enabled"
              checked={$form.enabled}
              onCheckedChange={(checked) => {
                $form.enabled = checked;
              }} />
            <span class="text-muted-foreground text-sm">Receive emails with new job matches</span>
          </div>
        </div>
      </div>

      <Dialog.Footer class="sm:justify-between">
        <Button type="button" variant="outline" onclick={onClose}>Cancel</Button>
        <Button type="submit" disabled={$isSubmitting} class="flex items-center gap-2">
          {#if $isSubmitting}
            <svg
              class="h-4 w-4 animate-spin"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24">
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            <span>Creating...</span>
          {:else}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
              <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
            </svg>
            <span>Create Alert</span>
          {/if}
        </Button>
      </Dialog.Footer>
    </form>
  </Dialog.Content>
</Dialog.Root>
