<script lang="ts">
  import { goto } from '$app/navigation';
  import type { Profile } from '@prisma/client';

  export let profiles: any[] = [];
  export let selectedProfileId: string | null = null;

  // Handle profile selection change
  const handleProfileChange = (event: Event) => {
    const select = event.target as HTMLSelectElement;
    const profileId = select.value;
    goto(`/dashboard/matches?profileId=${profileId}`);
  };
</script>

<div class="mb-6">
  <label for="profile-select" class="mb-1 block text-sm font-medium text-gray-700">
    Select Profile
  </label>
  <select
    id="profile-select"
    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
    value={selectedProfileId || ''}
    on:change={handleProfileChange}>
    {#each profiles as profile}
      <option value={profile.id}>{profile.name}</option>
    {/each}
  </select>
</div>
