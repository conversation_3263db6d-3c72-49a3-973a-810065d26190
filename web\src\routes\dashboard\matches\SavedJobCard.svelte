<script lang="ts">
  import { Trash2, MapPin, Calendar } from 'lucide-svelte';
  import { createEventDispatcher } from 'svelte';

  export let job: any;

  const dispatch = createEventDispatcher();

  function handleRemove() {
    dispatch('remove', { jobId: job.id });
  }
</script>

<div class="rounded-lg border p-4 transition-shadow hover:shadow-md">
  <!-- Job header -->
  <div class="mb-2 flex items-start justify-between">
    <div>
      <a href={`/dashboard/jobs/${job.id}`} class="text-blue-700 hover:underline">
        <h3 class="font-medium">{job.title}</h3>
      </a>
      <p class="text-sm text-gray-500">{job.company}</p>
    </div>

    <!-- Remove job button -->
    <button
      class="text-gray-500 hover:text-red-500"
      aria-label="Remove saved job"
      on:click={handleRemove}>
      <Trash2 class="h-4 w-4" />
      <span class="sr-only">Remove job</span>
    </button>
  </div>

  <!-- Job details -->
  <div class="mt-2 space-y-1">
    {#if job.location}
      <div class="flex items-center text-sm text-gray-500">
        <MapPin class="mr-1 h-4 w-4" />
        <span>{job.location}</span>
      </div>
    {/if}
    {#if job.postedDate}
      <div class="flex items-center text-sm text-gray-500">
        <Calendar class="mr-1 h-4 w-4" />
        <span>Posted {new Date(job.postedDate).toLocaleDateString()}</span>
      </div>
    {/if}
  </div>

  <!-- View details button -->
  <div class="mt-4 flex justify-end">
    <a
      href={`/dashboard/jobs/${job.id}`}
      class="rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700">
      View Details
    </a>
  </div>
</div>
