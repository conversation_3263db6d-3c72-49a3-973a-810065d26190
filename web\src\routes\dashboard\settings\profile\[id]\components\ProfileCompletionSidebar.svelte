<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import * as Progress from '$lib/components/ui/progress/index.js';
  import {
    Check,
    User,
    Phone,
    Briefcase,
    GraduationCap,
    FileText,
    Link,
    Wrench,
    Languages,
  } from 'lucide-svelte';
  import type { CompleteProfileSchema } from '$lib/validators/profile';

  // Props
  const { profile, onSectionClick } = $props<{
    profile: CompleteProfileSchema;
    onSectionClick: (section: string) => void;
  }>();

  // Completion items
  const completionItems = $derived(() => [
    {
      id: 'contact-info',
      label: 'Add your contact info',
      completed: !!profile.header?.fullName,
      percentage: 10,
      section: 'profile-header',
      icon: User,
    },
    {
      id: 'education',
      label: 'Add your education journey',
      completed: profile.educations && profile.educations.length > 0,
      percentage: 10,
      section: 'education',
      icon: GraduationCap,
    },
    {
      id: 'work-experience',
      label: 'Add your work experience',
      completed: profile.workExperiences && profile.workExperiences.length > 0,
      percentage: 20,
      section: 'work-experience',
      icon: Briefcase,
    },
    {
      id: 'resume',
      label: 'Add your resume',
      completed: !!profile.resume?.resumeId,
      percentage: 10,
      section: 'resume',
      icon: FileText,
    },
    {
      id: 'personal-links',
      label: 'Fill out your personal links',
      completed: !!(
        profile.portfolioLinks?.linkedinUrl ||
        profile.portfolioLinks?.githubUrl ||
        profile.portfolioLinks?.portfolioUrl
      ),
      percentage: 10,
      section: 'portfolio-links',
      icon: Link,
    },
    {
      id: 'skills',
      label: 'Add your skills',
      completed: profile.skills && profile.skills.skills && profile.skills.skills.length > 0,
      percentage: 20,
      section: 'skills',
      icon: Wrench,
    },
    {
      id: 'job-preferences',
      label: 'Fill out your job preferences',
      completed: !!profile.header?.jobSearchStatus,
      percentage: 10,
      section: 'profile-header',
      icon: Briefcase,
    },
    {
      id: 'languages',
      label: 'Add your languages',
      completed: profile.languages && profile.languages.length > 0,
      percentage: 10,
      section: 'languages',
      icon: Languages,
    },
  ]);

  // Completion percentage
  const completionPercentage = $derived(() => {
    const items = completionItems();
    const totalItems = items.length;
    const completedItems = items.filter((item) => item.completed).length;
    return Math.round((completedItems / totalItems) * 100);
  });

  // Get career level based on completion percentage
  const careerLevel = $derived(() => {
    const percentage = completionPercentage();
    return percentage < 25
      ? 'Career Newbie'
      : percentage < 50
        ? 'Career Explorer'
        : percentage < 75
          ? 'Career Professional'
          : percentage < 100
            ? 'Career Expert'
            : 'Career Master';
  });

  // Handle item click
  function handleItemClick(section: string) {
    onSectionClick(section);
  }
</script>

<div class="space-y-6">
  <!-- Profile Image and Name -->
  <div class="flex flex-col items-center text-center">
    <div class="relative mb-3">
      <div
        class="bg-primary/10 flex h-20 w-20 items-center justify-center overflow-hidden rounded-full">
        <span class="text-primary text-2xl font-bold">
          {profile.header?.fullName ? profile.header.fullName.substring(0, 2).toUpperCase() : 'CR'}
        </span>
      </div>
      <Button
        variant="outline"
        size="icon"
        class="absolute bottom-0 right-0 h-6 w-6 rounded-full"
        onclick={() => handleItemClick('profile-header')}>
        <User class="h-3 w-3" />
      </Button>
    </div>
    <h3 class="text-lg font-medium">
      {profile.header?.fullName || 'Your Name'}
    </h3>
    <p class="text-muted-foreground text-sm">
      {profile.header?.jobTitle || 'Job Title'}
    </p>
    <div class="mt-2 flex items-center">
      <span
        class={`mr-2 h-2 w-2 rounded-full ${
          profile.header?.jobSearchStatus === 'actively_looking'
            ? 'bg-green-500'
            : profile.header?.jobSearchStatus === 'open_to_opportunities'
              ? 'bg-yellow-500'
              : 'bg-gray-500'
        }`}></span>
      <p class="text-muted-foreground text-xs">
        {profile.header?.jobSearchStatus === 'actively_looking'
          ? 'Actively looking'
          : profile.header?.jobSearchStatus === 'open_to_opportunities'
            ? 'Open to opportunities'
            : 'Not looking'}
      </p>
    </div>
  </div>

  <!-- Career Hub -->
  <div>
    <h3 class="mb-2 text-sm font-medium">My Career Hub</h3>
    <div class="space-y-2">
      <Button
        variant="outline"
        class="w-full justify-start"
        onclick={() => handleItemClick('profile-header')}>
        <User class="mr-2 h-4 w-4" />
        <span>Profile</span>
      </Button>
      <Button
        variant="outline"
        class="w-full justify-start"
        onclick={() => handleItemClick('personal-info')}>
        <Phone class="mr-2 h-4 w-4" />
        <span>Personal Info</span>
      </Button>
      <Button
        variant="outline"
        class="w-full justify-start"
        onclick={() => handleItemClick('job-preferences')}>
        <Briefcase class="mr-2 h-4 w-4" />
        <span>Job Preferences</span>
      </Button>
    </div>
  </div>

  <!-- Profile Strength -->
  <div>
    <h3 class="mb-2 text-sm font-medium">My Profile Strength</h3>
    <div class="rounded-lg border p-4">
      <div class="flex items-center justify-between">
        <h4 class="font-medium">{careerLevel()}</h4>
        <span class="text-sm">{completionPercentage()}%</span>
      </div>
      <Progress.Root value={completionPercentage()} class="mt-2" />
      <p class="text-muted-foreground mt-2 text-xs">
        Complete your profile to autofill job applications effortlessly!
      </p>

      <div class="mt-4 space-y-2">
        {#each completionItems() as item}
          <div class="flex items-start">
            <div class="mt-0.5 flex h-4 w-4 items-center justify-center rounded-sm border">
              {#if item.completed}
                <Check class="text-primary h-3 w-3" />
              {/if}
            </div>
            <Button
              variant="link"
              size="sm"
              class="ml-2 h-auto p-0 text-sm font-normal"
              onclick={() => handleItemClick(item.section)}>
              {item.label}
              <span class="text-muted-foreground ml-1 text-xs">+{item.percentage}%</span>
            </Button>
          </div>
        {/each}
      </div>
    </div>
  </div>

  <!-- Upgrade CTA -->
  <div
    class="rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-900/20">
    <h3 class="mb-1 font-medium">Upgrade to Premium</h3>
    <p class="text-muted-foreground mb-3 text-sm">Supercharge your job search with AI features!</p>
    <Button variant="default" class="w-full">Subscribe to Premium</Button>
  </div>
</div>
