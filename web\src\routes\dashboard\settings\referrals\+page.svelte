<script lang="ts">
  import { onMount } from 'svelte';
  import { toast } from 'svelte-sonner';
  import { But<PERSON> } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Card } from '$lib/components/ui/card/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { Separator } from '$lib/components/ui/separator/index.js';
  import { Copy, RefreshCw, Users, Gift, Share2, ExternalLink } from 'lucide-svelte';

  let referralData: any = null;
  let loading = true;
  let customCode = '';
  let updating = false;
  let copying = false;

  // Load referral data
  const loadReferralData = async () => {
    try {
      const response = await fetch('/api/referrals');
      if (response.ok) {
        referralData = await response.json();
      } else {
        toast.error('Failed to load referral data');
      }
    } catch (error) {
      console.error('Error loading referral data:', error);
      toast.error('Failed to load referral data');
    } finally {
      loading = false;
    }
  };

  // Copy referral link to clipboard
  const copyReferralLink = async () => {
    if (!referralData?.referralLink) return;
    
    copying = true;
    try {
      await navigator.clipboard.writeText(referralData.referralLink);
      toast.success('Referral link copied to clipboard!');
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast.error('Failed to copy referral link');
    } finally {
      copying = false;
    }
  };

  // Generate new referral code
  const generateNewCode = async () => {
    updating = true;
    try {
      const response = await fetch('/api/referrals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'regenerate' }),
      });

      if (response.ok) {
        const data = await response.json();
        referralData.referralCode = data.referralCode;
        referralData.referralLink = data.referralLink;
        toast.success('New referral code generated!');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to generate new code');
      }
    } catch (error) {
      console.error('Error generating new code:', error);
      toast.error('Failed to generate new code');
    } finally {
      updating = false;
    }
  };

  // Set custom referral code
  const setCustomCode = async () => {
    if (!customCode.trim()) {
      toast.error('Please enter a custom code');
      return;
    }

    updating = true;
    try {
      const response = await fetch('/api/referrals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'create', customCode: customCode.trim() }),
      });

      if (response.ok) {
        const data = await response.json();
        referralData.referralCode = data.referralCode;
        referralData.referralLink = data.referralLink;
        customCode = '';
        toast.success('Custom referral code set!');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to set custom code');
      }
    } catch (error) {
      console.error('Error setting custom code:', error);
      toast.error('Failed to set custom code');
    } finally {
      updating = false;
    }
  };

  // Share referral link
  const shareReferralLink = async () => {
    if (!referralData?.referralLink) return;

    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Join Hirli with my referral link',
          text: 'Sign up for Hirli using my referral link and get started with job automation!',
          url: referralData.referralLink,
        });
      } catch (error) {
        console.error('Error sharing:', error);
        // Fallback to copying
        copyReferralLink();
      }
    } else {
      // Fallback to copying
      copyReferralLink();
    }
  };

  onMount(() => {
    loadReferralData();
  });
</script>

<div class="flex h-full flex-col">
  <div class="border-border flex flex-col justify-between border-b p-6">
    <h2 class="text-lg font-semibold">Referral Program</h2>
    <p class="text-muted-foreground">Share Hirli with friends and earn rewards for successful referrals.</p>
  </div>

  <div class="flex-1 p-6">
    {#if loading}
      <div class="flex items-center justify-center py-8">
        <div class="text-muted-foreground">Loading referral data...</div>
      </div>
    {:else if referralData}
      <div class="grid gap-6">
        <!-- Referral Stats -->
        <div class="grid gap-4 md:grid-cols-3">
          <Card.Root>
            <Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
              <Card.Title class="text-sm font-medium">Total Referrals</Card.Title>
              <Users class="h-4 w-4 text-muted-foreground" />
            </Card.Header>
            <Card.Content>
              <div class="text-2xl font-bold">{referralData.referralCount || 0}</div>
              <p class="text-xs text-muted-foreground">People you've referred</p>
            </Card.Content>
          </Card.Root>

          <Card.Root>
            <Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
              <Card.Title class="text-sm font-medium">Rewards Earned</Card.Title>
              <Gift class="h-4 w-4 text-muted-foreground" />
            </Card.Header>
            <Card.Content>
              <div class="text-2xl font-bold">$0</div>
              <p class="text-xs text-muted-foreground">Coming soon</p>
            </Card.Content>
          </Card.Root>

          <Card.Root>
            <Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
              <Card.Title class="text-sm font-medium">Your Code</Card.Title>
              <Share2 class="h-4 w-4 text-muted-foreground" />
            </Card.Header>
            <Card.Content>
              <div class="text-2xl font-bold font-mono">{referralData.referralCode}</div>
              <p class="text-xs text-muted-foreground">Your unique referral code</p>
            </Card.Content>
          </Card.Root>
        </div>

        <!-- Referral Link Section -->
        <Card.Root>
          <Card.Header>
            <Card.Title>Your Referral Link</Card.Title>
            <Card.Description>
              Share this link with friends to earn referral rewards when they sign up.
            </Card.Description>
          </Card.Header>
          <Card.Content class="space-y-4">
            <div class="flex gap-2">
              <Input
                value={referralData.referralLink}
                readonly
                class="font-mono text-sm" />
              <Button
                variant="outline"
                size="sm"
                on:click={copyReferralLink}
                disabled={copying}>
                <Copy class="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                on:click={shareReferralLink}>
                <Share2 class="h-4 w-4" />
              </Button>
            </div>
          </Card.Content>
        </Card.Root>

        <!-- Customize Referral Code -->
        <Card.Root>
          <Card.Header>
            <Card.Title>Customize Your Referral Code</Card.Title>
            <Card.Description>
              Create a custom referral code that's easy to remember and share.
            </Card.Description>
          </Card.Header>
          <Card.Content class="space-y-4">
            <div class="flex gap-2">
              <Input
                bind:value={customCode}
                placeholder="Enter custom code (4-12 characters)"
                maxlength="12"
                class="font-mono" />
              <Button
                on:click={setCustomCode}
                disabled={updating || !customCode.trim()}>
                Set Code
              </Button>
            </div>
            <div class="flex gap-2">
              <Button
                variant="outline"
                on:click={generateNewCode}
                disabled={updating}>
                <RefreshCw class="mr-2 h-4 w-4" />
                Generate New Code
              </Button>
            </div>
            <p class="text-xs text-muted-foreground">
              Custom codes must be 4-12 characters long and contain only letters and numbers.
            </p>
          </Card.Content>
        </Card.Root>

        <!-- Recent Referrals -->
        {#if referralData.referrals && referralData.referrals.length > 0}
          <Card.Root>
            <Card.Header>
              <Card.Title>Recent Referrals</Card.Title>
              <Card.Description>People who signed up using your referral code.</Card.Description>
            </Card.Header>
            <Card.Content>
              <div class="space-y-3">
                {#each referralData.referrals.slice(0, 5) as referral}
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="font-medium">{referral.referred.name || referral.referred.email}</p>
                      <p class="text-sm text-muted-foreground">
                        Joined {new Date(referral.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <Badge variant={referral.status === 'completed' ? 'default' : 'secondary'}>
                      {referral.status}
                    </Badge>
                  </div>
                  {#if referral !== referralData.referrals[referralData.referrals.length - 1]}
                    <Separator />
                  {/if}
                {/each}
              </div>
            </Card.Content>
          </Card.Root>
        {/if}

        <!-- Referred By -->
        {#if referralData.referredBy}
          <Card.Root>
            <Card.Header>
              <Card.Title>You Were Referred By</Card.Title>
            </Card.Header>
            <Card.Content>
              <div class="flex items-center gap-2">
                <p class="font-medium">{referralData.referredBy.name || referralData.referredBy.email}</p>
                <Badge variant="outline">Referrer</Badge>
              </div>
            </Card.Content>
          </Card.Root>
        {/if}
      </div>
    {:else}
      <div class="flex items-center justify-center py-8">
        <div class="text-muted-foreground">Failed to load referral data</div>
      </div>
    {/if}
  </div>
</div>
